"use strict";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};e.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},r=new e.Error().stack;r&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[r]="46153d43-187a-41c2-a1a0-7340494a80da",e._sentryDebugIdIdentifier="sentry-dbid-46153d43-187a-41c2-a1a0-7340494a80da")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const n=require("./sentry-filters.js");function d(e){try{if(e!=="renderer")return"https://<EMAIL>/4509942731112528"}catch{return"https://<EMAIL>/4509942731112528"}}function t(e="unknown"){try{if(e==="main"){const{app:r}=require("electron");return r.getVersion()}else return"2.1.3"}catch(r){return console.warn(`Failed to get version for ${e}:`,r),"2.1.3"}}function o(e="unknown"){try{return e==="main"?process.env.NODE_ENV==="production"?"production":"development":"production"}catch{return"production"}}function f(e){if(e.exception?.values)for(const r of e.exception.values){const i=r.value||"",s=r.type||"";if(n.shouldFilterByMessage(i)||n.shouldFilterByType(s))return!0;if(r.stacktrace?.frames)for(const l of r.stacktrace.frames){const u=l.filename||"";if(n.shouldFilterByFilename(u))return!0}}if(e.message&&n.shouldFilterByMessage(e.message)||e.level==="info"||e.level==="debug")return!0;if(e.tags){const r=["test","development-only","ignore"];for(const i of r)if(e.tags[i])return!0}return!1}function a(e){return function(r){return f(r)?(o(e)==="development"&&console.log(`[Sentry ${e}] Filtered error:`,r.exception?.values?.[0]?.value||r.message),null):(r.tags={...r.tags,process:e,platform:process.platform,version:t(e)},r.user={...r.user,process:e},r.contexts={...r.contexts,app:{name:"LanHai Translator",version:t(e)},runtime:{name:"electron-renderer",version:process.versions?.electron||"unknown"}},o(e)==="development"&&console.log(`[Sentry ${e}] Sending event:`,{message:r.message,error:r.exception?.values?.[0]?.value,tags:r.tags}),r)}}function c(){return{dsn:d("preload"),release:t("preload"),environment:o("preload"),beforeSend:a("preload")}}exports.createBeforeSend=a;exports.getAppVersion=t;exports.getEnvironment=o;exports.getPreloadConfig=c;
//# sourceMappingURL=sentry-config.js.map
