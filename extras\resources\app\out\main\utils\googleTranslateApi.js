"use strict";
!function() {
  try {
    var e = "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof globalThis ? globalThis : "undefined" != typeof self ? self : {};
    e.SENTRY_RELEASE = { id: "2.1.3" };
  } catch (e2) {
  }
}();
;
{
  try {
    (function() {
      var e = "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof globalThis ? globalThis : "undefined" != typeof self ? self : {}, n = new e.Error().stack;
      n && (e._sentryDebugIds = e._sentryDebugIds || {}, e._sentryDebugIds[n] = "81c96a31-debe-497c-9804-e8240656d5ab", e._sentryDebugIdIdentifier = "sentry-dbid-81c96a31-debe-497c-9804-e8240656d5ab");
    })();
  } catch (e) {
  }
}
;
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const electron = require("electron");
const googleEngineMap = /* @__PURE__ */ new Map([
  ["googlexjp", "https://www.google.com.sg"],
  ["googlehk", "https://www.google.com.hk"],
  ["googlehw", "https://www.google.com"]
]);
function parseGoogleAsyncHTML(html) {
  const extract = (id) => {
    const regex = new RegExp(`<span[^>]*id="${id}">(.*?)<\\/span>`);
    const match = html.match(regex);
    return match?.[1] ?? null;
  };
  return {
    sourceText: extract("tw-answ-source-text"),
    translatedText: extract("tw-answ-target-text"),
    detectedLangCode: extract("tw-answ-detected-sl"),
    romanization: extract("tw-answ-source-romanization"),
    sourceSpelling: extract("tw-answ-spelling"),
    langName: extract("tw-answ-language-detected")
  };
}
const langCodeMap = {
  en: "en",
  zh: "zh-CN",
  de: "de",
  th: "th",
  pt: "pt",
  fra: "fr",
  spa: "es",
  "ara": "ar",
  id: "id"
};
function googleAsyncTranslate({ text, fromLang, toLang, engineCode = "googlehk" }) {
  return new Promise((resolve, reject) => {
    const baseURL = googleEngineMap.get(engineCode);
    if (!baseURL) {
      console.error(`无效的 engineCode: ${engineCode}`);
      return resolve(null);
    }
    const id = Date.now();
    const mapLang = (code) => langCodeMap[code] || code;
    const postData = new URLSearchParams({
      async: `translate,sl:${mapLang(fromLang)},tl:${mapLang(toLang)},st:${encodeURIComponent(text)},id:${id},qc:true,ac:true,_id:tw-async-translate,_pms:s,_fmt:pc`
    }).toString();
    const request = electron.net.request({
      method: "POST",
      url: `${baseURL}/async/translate`
    });
    request.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
    request.setHeader("Referer", baseURL);
    request.setHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36");
    request.setHeader("Sec-CH-UA", '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"');
    request.setHeader("Sec-CH-UA-Mobile", "?0");
    request.setHeader("Sec-CH-UA-Platform", '"Windows"');
    request.setHeader("Sec-CH-UA-Arch", '"x86"');
    request.setHeader("Sec-CH-UA-Full-Version", '"138.0.7204.158"');
    let responseBody = "";
    request.on("response", (response) => {
      response.on("data", (chunk) => {
        responseBody += chunk.toString();
      });
      response.on("end", () => {
        try {
          const htmlParse = parseGoogleAsyncHTML(responseBody);
          const translated = htmlParse.translatedText;
          resolve({
            code: translated ? 1 : 4001,
            data: {
              ...htmlParse,
              result: translated
            }
          });
        } catch (err) {
          console.error("解析失败:", err.message);
          resolve(null);
        }
      });
    });
    request.on("error", (err) => {
      console.error("翻译请求失败:", err.message);
      resolve(null);
    });
    request.write(postData);
    request.end();
  });
}
exports.googleAsyncTranslate = googleAsyncTranslate;
exports.googleEngineMap = googleEngineMap;
//# sourceMappingURL=googleTranslateApi.js.map
