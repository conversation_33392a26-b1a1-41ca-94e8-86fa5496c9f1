"use strict";(function(){try{var i=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};i.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var i=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new i.Error().stack;e&&(i._sentryDebugIds=i._sentryDebugIds||{},i._sentryDebugIds[e]="1eb74a6a-e872-460d-8971-7d95c92bce73",i._sentryDebugIdIdentifier="sentry-dbid-1eb74a6a-e872-460d-8971-7d95c92bce73")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const a=require("electron"),d=require("./indexeddb.js"),c=require("../utils/performance.js"),l=require("dayjs"),f=require("./googleTranslateApi.js");class h{config=null;dependPlatform=null;isNotMoney=!1;db=null;memoryCache=new Map;maxCacheSize=1e3;debouncedTranslate=null;init(e){this.db=new d.MyIdb,this.dependPlatform=e,this.debouncedTranslate=c.debounce(this._performTranslate.bind(this),300),this.setTranslateConfig(),a.ipcRenderer.on("updateViewTranslateConfig",(t,n)=>{this.setTranslateConfig(n),this.clearCache()})}async setTranslateConfig(e){if(e){this.config=e,this.dependPlatform&&this.dependPlatform.translateList();return}if(this.dependPlatform){const t=await a.ipcRenderer.invoke("getTranslateConfig",{sessionId:this.dependPlatform.viewSessionId,isChating:!0});t&&(this.config=t)}else throw new Error("dependPlatform is null please set....")}generateCacheKey(e){return`${e.text}-${e.toLang}-${e.engineCode||"default"}`}getFromMemoryCache(e){const t=this.memoryCache.get(e);return t&&Date.now()-t.timestamp<18e6?t.result:null}setToMemoryCache(e,t){if(this.memoryCache.size>=this.maxCacheSize){const n=this.memoryCache.keys().next().value;this.memoryCache.delete(n)}this.memoryCache.set(e,{result:t,timestamp:Date.now()})}clearCache(){this.memoryCache.clear(),console.log("[Translater] Cache cleared")}async _translate(e){if(!e.text||!this.config?.self)return;if(/^[\d_\- .!@#$%^&*()+=]+$/.test(e.text))return e.text;const t=await this.getResult(e);if(t&&t===e.text){if(/[\u4e00-\u9fa5]/.test(t)&&e.toLang!=="zh")return"";if(this.config.send_lang!==this.config.recive_lang)return e.text}else return t===""?e.isInput&&e.toLang===this.config.recive_lang?e.text:"😣😣😣翻译失败...换一个引擎试试吧":t}async getResult(e){const t=this.generateCacheKey(e);let n=this.getFromMemoryCache(t);return n?this.formatResponse(n):(n=await this.db.getTranslate(e),n?this.setToMemoryCache(t,n):n=await this._performTranslate(e),this.formatResponse(n)||"")}historyTemp=[];timmer;async _performTranslate(e){try{let t;if(f.googleEngineMap.has(e.engineCode)?t=await a.ipcRenderer.invoke("googleAsyncTranslate",e):t=await a.ipcRenderer.invoke("getMessageTranslate",JSON.stringify(e)),t.code===1){const n={text:e.text,toLang:e.toLang,transText:t.data.result};if(e.isInput){if(/[\u4e00-\u9fa5]/.test(t.data.result)&&e.toLang!=="zh")return alert("字符过多 任何数据不发送......"),{};n.recive_lang=this.config.recive_lang,n.isInput=e.isInput}const s=this.generateCacheKey(e);this.setToMemoryCache(s,t),this.db.addTranslate(n),this.historyTemp.push({...e,translateText:t.data.result}),this.timmer&&clearTimeout(this.timmer),this.timmer=setTimeout(()=>{this.historyTemp.length>0&&(a.ipcRenderer.send("getSaveChatLog",JSON.stringify(this.historyTemp)),this.historyTemp=[])},1e3),this.isNotMoney&&(this.isNotMoney=!1)}else t.code===400010&&this.isNotMoney===!1&&(alert(`${t.msg}......`),this.isNotMoney=!0);return t}catch(t){return console.error("[Translater] Translation failed:",t),null}}async translateInput(e){if(!(this.config&&this.config.trans_over))return e;let t={text:e.split(`
`).join(""),engineCode:this.config.engineCode,fromLang:"auto",toLang:this.config.send_lang,recive_lang:this.config.recive_lang,time:l(Date.now()).format("YYYY-MM-DD HH:mm:ss"),type:"out",ccountId:this.dependPlatform.userId,fansId:this.dependPlatform.chatUserId,platform:this.dependPlatform.platform};const n=await this._translate({isInput:!0,...t});return n==="😣😣😣翻译失败...换一个引擎试试吧"?(alert("翻译失败... 无法发送😶😶😶"),""):n}createLoadingDiv(e){var t=document.createElement("div");return t.style.display="flex",t.style.flexDirection="column",t.insertAdjacentHTML("afterbegin",`<span>${e}</span>`),t.insertAdjacentHTML("beforeend",`<div style="width:100%; margin: 5px 0px; height: 0; border-bottom: 1px dashed #000"></div>
      <div class="__translateloading">
      <div class="dot"></div>
      <div class="dot"></div>
      <div class="dot"></div>
      </div>`),t}async translateMessage(e,t={}){if(!this.config.self)return;if(e.hasAttribute("data-translated")){if(e.getAttribute("data-translated")===this.config.recive_lang||e.getAttribute("data-translated")==="tranlating")return!0}else e.setAttribute("data-originaltext",e.innerText);e.setAttribute("data-translated","tranlating"),e.replaceChildren(this.createLoadingDiv(e.innerText));let n=e.getAttribute("data-originaltext").trim(),s={text:n.split(`
`).join(""),engineCode:this.config.engineCode,fromLang:"auto",toLang:this.config.recive_lang,time:l(Date.now()).format("YYYY-MM-DD HH:mm:ss"),type:t.type||"out",ccountId:this.dependPlatform.userId,fansId:t.fansId||this.dependPlatform.chatUserId,platform:this.dependPlatform.platform};const o=await this._translate(s);var r=document.createElement("div");r.insertAdjacentHTML("beforeend",`<span>${n}</span>`),o&&(r.insertAdjacentHTML("beforeend",'<div style="width:100%; margin: 5px 0px; height: 0; border-bottom: 1px dashed currentColor"></div>'),r.insertAdjacentHTML("beforeend",`<span>${o}</span>`)),e.replaceChildren(r),e.setAttribute("data-translated",this.config.recive_lang?this.config.recive_lang:""),t.callback&&t.callback()}formatResponse(e){let t="";return e.hasOwnProperty("code")&&e.code===1?t=e.data.result:e.value&&(t=e.value),t}}exports.Translater=h;
//# sourceMappingURL=index.js.map
