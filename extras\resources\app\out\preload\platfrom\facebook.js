"use strict";(function(){try{var o=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};o.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var o=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},t=new o.Error().stack;t&&(o._sentryDebugIds=o._sentryDebugIds||{},o._sentryDebugIds[t]="09b525de-2ecf-4dbc-9d3f-2c5e852ba72e",o._sentryDebugIdIdentifier="sentry-dbid-09b525de-2ecf-4dbc-9d3f-2c5e852ba72e")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});require("electron");require("idb");require("crypto-js");require("dayjs");const y=require("./platfrom.js");class g extends y.Platform{constructor(t){super(t)}idRegular=/(?<=\/t\/)[\d]{1,}(?=\/?)|$/;userId=void 0;userName=void 0;prefilePicture=void 0;chatUserId=void 0;isFull=!0;Store;getStore(){}async init(t){await super.init(t),this.getUserId(),this.bindInputFunction(),this.smallBindInputFunction()}_o(t,s){this.getUserData(),this.bindInputFunction(),this.smallBindInputFunction(),this.translateList(),this.getUnreadSessionUserToFans(),this.getUserInfo(),this.autoReplyHandler(),this.getSmallChatUserInfo()}_u(t){this.isFull=this.isFullScreenChat(),this.getCurrentSessionUserId()}getCurrentSessionUserId(){if(this.isFull&&this.idRegular.test(location.pathname)){const t=()=>{let s=this.idRegular.exec(location.pathname)[0];if(s==this.userId)return;this.chatUserId=s;let e="",i=document.querySelector(this.sI.sessionListElSelector);if(i){i.querySelectorAll(this.sI.sessionUserA)?.forEach(l=>{this.idRegular.exec(l.href)[0]===s&&(e=l.querySelector(this.sI.sessionUserNickNameElSelector)?.innerText)});let a={mainAccount:this.userId,fansId:s,nickName:e||s,platform:this.platform};this.sendCurrentSessionFansInfo(a)}else setTimeout(()=>{window.requestAnimationFrame(t)},500)};window.requestAnimationFrame(t)}}getUnreadSessionUserToFans(){if(this.isFull&&this.idRegular.test(location.pathname)){const s=document.querySelector(this.sI.sessionListElSelector)?.querySelectorAll(this.sI.unreadElSelector);if(s&&(this.sendUnReadCount(s.length),s.length===0))return;const e=[];s?.forEach(i=>{if(i.hasAttribute("aira-isread"))return;i.setAttribute("aira-isread","true");let n=i.closest("a");if(!n)return;let a=n.querySelector(this.sI.sessionUserNickNameElSelector)?.innerText,l=this.idRegular.exec(n.href);l&&e.push({id:l[0],nickName:a})}),e.length>0&&this.sendNewFansList({viewSessionId:this.viewSessionId,platform:this.platform,mainAccount:this.userId,unreadListInfo:e})}}autoReplyHandler(){this.isFull&&this.idRegular.test(location.pathname)&&this.aiReply(async()=>{const s=document.querySelector(this.sI.sessionListElSelector),e=s?.querySelectorAll(this.sI.unreadElSelector);let i=s?.querySelectorAll(this.sI.sessionUserA);if(e)for(const n of e)await(async()=>{let a=n.closest("a");if(!a)return;a?.click();const l=n?.getBoundingClientRect();return this.clickAt({x:Math.round(l.left+l.width/2),y:Math.round(l.top+l.height/2)}),new Promise((r,m)=>{setTimeout(async()=>{let u=Array.from(document.querySelectorAll("[data-originaltext]")),c="",S=u.slice(-5).map(h=>{let I=h.innerText.split(`
`)[0],p="";return h.closest(this.sI.reciveMessageElSelector)?(p="user",c=I):p="assistant",{content:I,role:p}});console.log("m:",S);let f=await this.getAiReply({question:c,messageList:S,chat_id:this.userId});if(f&&!f.error){await this.inputMessage(f.content,!0);let h=i[i.length-1];h?.click();const d=h?.getBoundingClientRect();this.clickAt({x:Math.round(d.left+d.width/2),y:Math.round(d.top+d.height/2)})}else console.log("ai error",f);r()},2e3)})})()})}clearInput(t){let s={};navigator.userAgentData?.platform==="macOS"||/Mac/.test(navigator.userAgent)?s={key:"a",metaKey:!0,bubbles:!0,cancelable:!0}:s={key:"a",ctrlKey:!0,bubbles:!0,cancelable:!0};const e=new KeyboardEvent("keydown",s),i=new KeyboardEvent("keydown",{key:"Backspace",bubbles:!0,cancelable:!0});let n;if(this.isFull)n=document.querySelector(t);else{const a=document.querySelector(this.sI.activeSmall);a&&(n=a.querySelector(t))}n.dispatchEvent(e),n.dispatchEvent(i)}bindInputFunction(){if(this.isFull&&this.idRegular.test(location.pathname)){const t=s=>{const e=document.querySelector(this.sI.inputElSelector);e?(e.addEventListener("keydown",async i=>{if(e.setAttribute("aria-bind","true"),(i.key==="Enter"||i.keyCode===13)&&!i.shiftKey){i.stopPropagation(),i.stopImmediatePropagation(),this.changeInputEditStatus(!1,this.sI.inputElSelector);const n=i.target.innerText;if(this.clearInput(this.sI.inputElSelector),n.trim()){let a=await this.translater.translateInput(n);a?await this.inputMessage(a,!0):await this.inputMessage(n,!1),console.log("input end")}else await this.inputMessage(n,!0);this.changeInputEditStatus(!0,this.sI.inputElSelector),this.switchShadowState(!1)}},{capture:!0},!0),e.addEventListener("input",i=>{i.stopPropagation(),i.stopImmediatePropagation(),this.createMaskDiv()})):setTimeout(()=>{window.requestAnimationFrame(t)},500)};window.requestAnimationFrame(t)}}sendMessageToInput({type:t,message:s}){this.inputMessage(s,t==="send")}inputMessage(t,s=!1){return console.log("value,",t),new Promise(e=>{const i=document.querySelector(this.sI.activeSmall);let n;this.isFull?n=document.querySelector(this.sI.inputElSelector):i&&(n=i.querySelector(this.sI.smallInputElSelector)),n?.focus();const a=new InputEvent("input",{bubbles:!0,cancelable:!0,data:t,inputType:"insertText"});n.dispatchEvent(a),s?setTimeout(()=>{this.isFull?document.querySelector(this.sI.sendButtonElSelector).click():i&&i.querySelector(this.sI.smallSendButtonElSelector)?.click(),e()},100):e()})}switchShadowState(t){const s=document.querySelector(this.sI.activeSmall);let e;this.isFull?e=document.querySelector(".myShadow"):e=s.querySelector(".myShadow"),e&&(t?e.style.display="block":e.style.display="none")}createMaskDiv(){const t=document.querySelector(this.sI.activeSmall);if(this.isFull?!document.querySelector(".myShadow"):!t?.querySelector(".myShadow")){let e=document.createElement("div");e.className="myShadow",e.style.position="absolute",e.style.width="100%",e.style.height="100%",e.style.top="0px",e.style.right="0px",e.style.zIndex=9999;let i,n;this.isFull?(i=this.sI.inputElSelector,n=document.querySelector(this.sI.sendButtonElSelector)):(i=this.sI.smallInputElSelector,t&&(n=t.querySelector(this.sI.smallSendButtonElSelector))),n&&(n.style.position="relative",n.appendChild(e)),e.addEventListener("click",async a=>{a.stopPropagation(),this.changeInputEditStatus(!1,i);let l=this.translater.config;const r=l&&l.trans_over,u=(this.isFull?document.querySelector(i):t?.querySelector(i)).innerText;if(console.log(u),this.clearInput(i),r&&u.trim()){let c=await this.translater.translateInput(u);c?await this.inputMessage(c,!0):await this.inputMessage(u,!1)}else await this.inputMessage(u,!0);this.changeInputEditStatus(!0,i),this.switchShadowState(!1)})}else this.switchShadowState(!0)}changeInputEditStatus(t,s){try{let e;if(this.isFull)e=document.querySelector(s);else{const i=document.querySelector(this.sI.activeSmall);i&&(e=i.querySelector(s))}if(!e)return;t?(e.removeAttribute("contenteditable"),e.setAttribute("contenteditable","true")):(e.removeAttribute("contenteditable"),e.setAttribute("contenteditable","false"))}catch(e){console.error(e)}}async getUserId(){let t=await cookieStore.get("c_user");if(t){this.userId=t.value;let s={userId:this.userId,platform:this.platform,phone:this.userId,nickName:this.userName||this.userId,session_id:this.viewSessionId};this.sendPlatformUserInfo(s)}}getUserData(){if(!this.userData)try{this.userData=window.require("CurrentUserInitialData")}catch(t){console.log(t)}}async getUserInfo(){let t=!1;if(this.userData&&(!this.userName||!this.userId)&&(this.userName=this.userData.NAME,this.userId=this.userData.USER_ID,t=!0),t){let s={userId:this.userId,platform:this.platform,phone:this.userId,nickName:this.userName||this.userId,session_id:this.viewSessionId};this.sendPlatformUserInfo(s)}}async translateList(){const t=(s,e)=>{let i=s.querySelector(this.sI.messageListElSelector);i&&(i.querySelectorAll(this.sI.reciveMessageElSelector).forEach((l,r)=>{this.translater.translateMessage(l,{fansId:e,type:"in"})}),i.querySelectorAll(this.sI.sendMessageElSelector).forEach((l,r)=>{this.translater.translateMessage(l,{fansId:e,type:"out"})}))};if(this.isFull)t(document);else{const s=document.querySelector(this.sI.activeSmall);if(s){let e=s.querySelector(this.sI.activeSmallId)?.getAttribute("href");e&&(e=e.replace(/\//g,""),t(s,e))}}}getSmallChatUserInfo(){const t=document.querySelector(this.sI.activeSmall);if(t){let s=t.querySelector(this.sI.activeSmallId)?.getAttribute("href"),e=t.querySelector(this.sI.activeSmallNickName)?.innerText;if(s&&(s=s.replace(/\//g,""),this.chatUserId!=s)){this.chatUserId=s;let i={mainAccount:this.userId,fansId:s,nickName:e||s,platform:this.platform};console.log("小窗聊天更新聊天人信息",i),this.sendCurrentSessionFansInfo(i)}}}smallBindInputFunction(){const t=document.querySelector(this.sI.activeSmall);if(t){const s=e=>{const i=t.querySelector(this.sI.smallInputElSelector);i?(i.addEventListener("keydown",async n=>{if(i.setAttribute("aria-bind","true"),(n.key==="Enter"||n.keyCode===13)&&!n.shiftKey){n.stopPropagation(),n.stopImmediatePropagation(),this.changeInputEditStatus(!1,this.sI.smallInputElSelector);let a=this.translater.config;const l=a&&a.trans_over,r=n.target.innerText;if(this.clearInput(this.sI.smallInputElSelector),l&&r.trim()){let m=await this.translater.translateInput(r);m?await this.inputMessage(m,!0):await this.inputMessage(r,!1)}this.changeInputEditStatus(!0,this.sI.smallInputElSelector),this.switchShadowState(!1)}},{capture:!0},!0),i.addEventListener("input",n=>{n.stopPropagation(),n.stopImmediatePropagation(),this.createMaskDiv()})):setTimeout(()=>{window.requestAnimationFrame(s)},500)};window.requestAnimationFrame(s)}}isFullScreenChat(){return location.pathname.includes("/messages/t/")||location.pathname.includes("/messages/e2ee/t/")}}exports.FacebookHandler=g;
//# sourceMappingURL=facebook.js.map
