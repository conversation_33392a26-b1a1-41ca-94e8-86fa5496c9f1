{"version": "0.2.0", "configurations": [{"name": "Wails: Production installer", "type": "go", "request": "launch", "mode": "exec", "program": "${workspaceFolder}/build/bin/installer.exe", "preLaunchTask": "build", "cwd": "${workspaceFolder}"}, {"name": "Wails: Debug installer", "type": "go", "request": "launch", "mode": "exec", "program": "${workspaceFolder}/build/bin/installer.exe", "preLaunchTask": "build debug", "cwd": "${workspaceFolder}"}, {"name": "Wails: Dev installer", "type": "go", "request": "launch", "mode": "exec", "program": "${workspaceFolder}/build/bin/installer.exe", "preLaunchTask": "build dev", "cwd": "${workspaceFolder}"}]}