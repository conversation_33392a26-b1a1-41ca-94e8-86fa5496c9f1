"use strict";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};t.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var t=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new t.Error().stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="b3be5edc-71f6-4736-99d3-b964296c76aa",t._sentryDebugIdIdentifier="sentry-dbid-b3be5edc-71f6-4736-99d3-b964296c76aa")})()}catch{}class n{constructor(e){this.setStyle(e),this.create()}inheritText="翻译后内容......";dStyle={width:"200px",minHeight:"30px",maxHeight:"300px",backgroundColor:"#ffffff",color:"#5f5f5f",display:"none",alignItems:"center",justifyContent:"justify-start",position:"fixed",overflow:"auto",lineHeight:"22px",fontSize:"16px",zIndex:"555"};d=null;create(){if(this.d)return this.d;const e=document.createElement("div"),i=document.createElement("div");return i.style.background="rgba(194, 189, 184, .15)",i.style.borderRadius="5px",i.style.padding="5px",i.style.width="100%",e.appendChild(i),Object.assign(e.style,this.dStyle),i.textContent=this.inheritText,document.body.append(e),this.d=e,e}show(){this.d.style.display="flex"}hide(){this.d.style.display="none"}setStyle(e){e&&(Object.assign(this.dStyle,e),this.upDStyle())}setRect(e){this.setStyle(e),this.upDStyle()}upDStyle(){Object.assign(this.d.style,this.dStyle)}setTextContent(e){e?this.d.querySelector("div").textContent=e:this.d.querySelector("div").textContent=this.inheritText}}module.exports=n;
//# sourceMappingURL=translateContrast.js.map
