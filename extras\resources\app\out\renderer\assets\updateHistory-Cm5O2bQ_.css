/*! 
 * OverlayScrollbars
 * Version: 2.11.5
 * 
 * Copyright (c) <PERSON> | KingSora.
 * https://github.com/KingSora
 * 
 * Released under the MIT license.
 */.os-size-observer,.os-size-observer-listener{scroll-behavior:auto!important;direction:inherit;pointer-events:none;overflow:hidden;visibility:hidden;box-sizing:border-box}.os-size-observer,.os-size-observer-listener,.os-size-observer-listener-item,.os-size-observer-listener-item-final{writing-mode:horizontal-tb;position:absolute;left:0;top:0}.os-size-observer{z-index:-1;contain:strict;display:flex;flex-direction:row;flex-wrap:nowrap;padding:inherit;border:inherit;box-sizing:inherit;margin:-133px;inset:0;transform:scale(.1)}.os-size-observer:before{content:"";flex:none;box-sizing:inherit;padding:10px;width:10px;height:10px}.os-size-observer-appear{animation:os-size-observer-appear-animation 1ms forwards}.os-size-observer-listener{box-sizing:border-box;position:relative;flex:auto;padding:inherit;border:inherit;margin:-133px;transform:scale(10)}.os-size-observer-listener.ltr{margin-right:-266px;margin-left:0}.os-size-observer-listener.rtl{margin-left:-266px;margin-right:0}.os-size-observer-listener:empty:before{content:"";width:100%;height:100%}.os-size-observer-listener:empty:before,.os-size-observer-listener>.os-size-observer-listener-item{display:block;position:relative;padding:inherit;border:inherit;box-sizing:content-box;flex:auto}.os-size-observer-listener-scroll{box-sizing:border-box;display:flex}.os-size-observer-listener-item{right:0;bottom:0;overflow:hidden;direction:ltr;flex:none}.os-size-observer-listener-item-final{transition:none}@keyframes os-size-observer-appear-animation{0%{cursor:auto}to{cursor:none}}.os-trinsic-observer{flex:none;box-sizing:border-box;position:relative;max-width:0px;max-height:1px;padding:0;margin:0;border:none;overflow:hidden;z-index:-1;height:0;top:calc(100% + 1px);contain:strict}.os-trinsic-observer:not(:empty){height:calc(100% + 1px);top:-1px}.os-trinsic-observer:not(:empty)>.os-size-observer{width:1000%;height:1000%;min-height:1px;min-width:1px}[data-overlayscrollbars-initialize]:not([data-overlayscrollbars-viewport]),[data-overlayscrollbars-viewport~=scrollbarHidden]{scrollbar-width:none!important}[data-overlayscrollbars-initialize]:not([data-overlayscrollbars-viewport])::-webkit-scrollbar,[data-overlayscrollbars-initialize]:not([data-overlayscrollbars-viewport])::-webkit-scrollbar-corner,[data-overlayscrollbars-viewport~=scrollbarHidden]::-webkit-scrollbar,[data-overlayscrollbars-viewport~=scrollbarHidden]::-webkit-scrollbar-corner{-webkit-appearance:none!important;appearance:none!important;display:none!important;width:0!important;height:0!important}[data-overlayscrollbars-initialize]:not([data-overlayscrollbars]):not(html):not(body){overflow:auto}html[data-overlayscrollbars-body]{overflow:hidden}html[data-overlayscrollbars-body],html[data-overlayscrollbars-body]>body{width:100%;height:100%;margin:0}html[data-overlayscrollbars-body]>body{overflow:visible;margin:0}[data-overlayscrollbars]{position:relative}[data-overlayscrollbars~=host],[data-overlayscrollbars-padding]{display:flex;align-items:stretch!important;flex-direction:row!important;flex-wrap:nowrap!important;scroll-behavior:auto!important}[data-overlayscrollbars-padding],[data-overlayscrollbars-viewport]:not([data-overlayscrollbars]){box-sizing:inherit;position:relative;flex:auto;height:auto;width:100%;min-width:0;padding:0;margin:0;border:none;z-index:0}[data-overlayscrollbars-viewport]:not([data-overlayscrollbars]){--os-vaw: 0;--os-vah: 0;outline:none}[data-overlayscrollbars-viewport]:not([data-overlayscrollbars]):focus{outline:none}[data-overlayscrollbars-viewport][data-overlayscrollbars-viewport~=arrange]:before{content:"";position:absolute;pointer-events:none;z-index:-1;min-width:1px;min-height:1px;width:var(--os-vaw);height:var(--os-vah)}[data-overlayscrollbars~=host],[data-overlayscrollbars-padding]{overflow:hidden!important}[data-overlayscrollbars~=host][data-overlayscrollbars~=noClipping],[data-overlayscrollbars-padding~=noClipping]{overflow:visible!important}[data-overlayscrollbars-viewport]{--os-viewport-overflow-x: hidden;--os-viewport-overflow-y: hidden;overflow-x:var(--os-viewport-overflow-x);overflow-y:var(--os-viewport-overflow-y)}[data-overlayscrollbars-viewport~=overflowXVisible]{--os-viewport-overflow-x: visible}[data-overlayscrollbars-viewport~=overflowXHidden]{--os-viewport-overflow-x: hidden}[data-overlayscrollbars-viewport~=overflowXScroll]{--os-viewport-overflow-x: scroll}[data-overlayscrollbars-viewport~=overflowYVisible]{--os-viewport-overflow-y: visible}[data-overlayscrollbars-viewport~=overflowYHidden]{--os-viewport-overflow-y: hidden}[data-overlayscrollbars-viewport~=overflowYScroll]{--os-viewport-overflow-y: scroll}[data-overlayscrollbars-viewport~=overflowImportant]{overflow-x:var(--os-viewport-overflow-x)!important;overflow-y:var(--os-viewport-overflow-y)!important}[data-overlayscrollbars-viewport~=noContent]:not(#osFakeId){font-size:0!important;line-height:0!important}[data-overlayscrollbars-viewport~=noContent]:not(#osFakeId):before,[data-overlayscrollbars-viewport~=noContent]:not(#osFakeId):after,[data-overlayscrollbars-viewport~=noContent]:not(#osFakeId)>*{display:none!important;position:absolute!important;width:1px!important;height:1px!important;padding:0!important;margin:-1px!important;overflow:hidden!important;clip:rect(0,0,0,0)!important;white-space:nowrap!important;border-width:0!important}[data-overlayscrollbars-viewport~=measuring],[data-overlayscrollbars-viewport~=scrolling]{scroll-behavior:auto!important;scroll-snap-type:none!important}[data-overlayscrollbars-viewport~=measuring][data-overlayscrollbars-viewport~=overflowXVisible]{overflow-x:hidden!important}[data-overlayscrollbars-viewport~=measuring][data-overlayscrollbars-viewport~=overflowYVisible]{overflow-y:hidden!important}[data-overlayscrollbars-content]{box-sizing:inherit}[data-overlayscrollbars-contents]:not(#osFakeId):not([data-overlayscrollbars-padding]):not([data-overlayscrollbars-viewport]):not([data-overlayscrollbars-content]){display:contents}[data-overlayscrollbars-grid],[data-overlayscrollbars-grid] [data-overlayscrollbars-padding]{display:grid;grid-template:1fr/1fr}[data-overlayscrollbars-grid]>[data-overlayscrollbars-padding],[data-overlayscrollbars-grid]>[data-overlayscrollbars-viewport],[data-overlayscrollbars-grid]>[data-overlayscrollbars-padding]>[data-overlayscrollbars-viewport]{height:auto!important;width:auto!important}@property --os-scroll-percent{syntax: "<number>"; inherits: true; initial-value: 0;}@property --os-viewport-percent{syntax: "<number>"; inherits: true; initial-value: 0;}.os-scrollbar{--os-viewport-percent: 0;--os-scroll-percent: 0;--os-scroll-direction: 0;--os-scroll-percent-directional: calc( var(--os-scroll-percent) - (var(--os-scroll-percent) + (1 - var(--os-scroll-percent)) * -1) * var(--os-scroll-direction) )}.os-scrollbar{contain:size layout;contain:size layout style;transition:opacity .15s,visibility .15s,top .15s,right .15s,bottom .15s,left .15s;pointer-events:none;position:absolute;opacity:0;visibility:hidden}body>.os-scrollbar{position:fixed;z-index:99999}.os-scrollbar-transitionless{transition:none!important}.os-scrollbar-track{position:relative;padding:0!important;border:none!important}.os-scrollbar-handle{position:absolute}.os-scrollbar-track,.os-scrollbar-handle{pointer-events:none;width:100%;height:100%}.os-scrollbar.os-scrollbar-track-interactive .os-scrollbar-track,.os-scrollbar.os-scrollbar-handle-interactive .os-scrollbar-handle{pointer-events:auto;touch-action:none}.os-scrollbar-horizontal{bottom:0;left:0}.os-scrollbar-vertical{top:0;right:0}.os-scrollbar-rtl.os-scrollbar-horizontal{right:0}.os-scrollbar-rtl.os-scrollbar-vertical{right:auto;left:0}.os-scrollbar-visible{opacity:1;visibility:visible}.os-scrollbar-auto-hide.os-scrollbar-auto-hide-hidden{opacity:0;visibility:hidden}.os-scrollbar-interaction.os-scrollbar-visible{opacity:1;visibility:visible}.os-scrollbar-unusable,.os-scrollbar-unusable *,.os-scrollbar-wheel,.os-scrollbar-wheel *{pointer-events:none!important}.os-scrollbar-unusable .os-scrollbar-handle{opacity:0!important;transition:none!important}.os-scrollbar-horizontal .os-scrollbar-handle{bottom:0;left:calc(var(--os-scroll-percent-directional) * 100%);transform:translate(calc(var(--os-scroll-percent-directional) * -100%));width:calc(var(--os-viewport-percent) * 100%)}.os-scrollbar-vertical .os-scrollbar-handle{right:0;top:calc(var(--os-scroll-percent-directional) * 100%);transform:translateY(calc(var(--os-scroll-percent-directional) * -100%));height:calc(var(--os-viewport-percent) * 100%)}@supports (container-type: size){.os-scrollbar-track{container-type:size}.os-scrollbar-horizontal .os-scrollbar-handle{left:auto;transform:translate(calc(var(--os-scroll-percent-directional) * 100cqw + var(--os-scroll-percent-directional) * -100%))}.os-scrollbar-vertical .os-scrollbar-handle{top:auto;transform:translateY(calc(var(--os-scroll-percent-directional) * 100cqh + var(--os-scroll-percent-directional) * -100%))}.os-scrollbar-rtl.os-scrollbar-horizontal .os-scrollbar-handle{right:auto;left:0}}.os-scrollbar-rtl.os-scrollbar-vertical .os-scrollbar-handle{right:auto;left:0}.os-scrollbar.os-scrollbar-horizontal.os-scrollbar-cornerless,.os-scrollbar.os-scrollbar-horizontal.os-scrollbar-cornerless.os-scrollbar-rtl{left:0;right:0}.os-scrollbar.os-scrollbar-vertical.os-scrollbar-cornerless,.os-scrollbar.os-scrollbar-vertical.os-scrollbar-cornerless.os-scrollbar-rtl{top:0;bottom:0}@media print{.os-scrollbar{display:none}}.os-scrollbar{--os-size: 0;--os-padding-perpendicular: 0;--os-padding-axis: 0;--os-track-border-radius: 0;--os-track-bg: none;--os-track-bg-hover: none;--os-track-bg-active: none;--os-track-border: none;--os-track-border-hover: none;--os-track-border-active: none;--os-handle-border-radius: 0;--os-handle-bg: none;--os-handle-bg-hover: none;--os-handle-bg-active: none;--os-handle-border: none;--os-handle-border-hover: none;--os-handle-border-active: none;--os-handle-min-size: 33px;--os-handle-max-size: none;--os-handle-perpendicular-size: 100%;--os-handle-perpendicular-size-hover: 100%;--os-handle-perpendicular-size-active: 100%;--os-handle-interactive-area-offset: 0}.os-scrollbar-track{border:var(--os-track-border);border-radius:var(--os-track-border-radius);background:var(--os-track-bg);transition:opacity .15s,background-color .15s,border-color .15s}.os-scrollbar-track:hover{border:var(--os-track-border-hover);background:var(--os-track-bg-hover)}.os-scrollbar-track:active{border:var(--os-track-border-active);background:var(--os-track-bg-active)}.os-scrollbar-handle{border:var(--os-handle-border);border-radius:var(--os-handle-border-radius);background:var(--os-handle-bg)}.os-scrollbar-handle:hover{border:var(--os-handle-border-hover);background:var(--os-handle-bg-hover)}.os-scrollbar-handle:active{border:var(--os-handle-border-active);background:var(--os-handle-bg-active)}.os-scrollbar-track:before,.os-scrollbar-handle:before{content:"";position:absolute;inset:0;display:block}.os-scrollbar-horizontal{padding:var(--os-padding-perpendicular) var(--os-padding-axis);right:var(--os-size);height:var(--os-size)}.os-scrollbar-horizontal.os-scrollbar-rtl{left:var(--os-size);right:0}.os-scrollbar-horizontal .os-scrollbar-track:before{top:calc(var(--os-padding-perpendicular) * -1);bottom:calc(var(--os-padding-perpendicular) * -1)}.os-scrollbar-horizontal .os-scrollbar-handle{min-width:var(--os-handle-min-size);max-width:var(--os-handle-max-size);height:var(--os-handle-perpendicular-size);transition:opacity .15s,background-color .15s,border-color .15s,height .15s}.os-scrollbar-horizontal .os-scrollbar-handle:before{top:calc((var(--os-padding-perpendicular) + var(--os-handle-interactive-area-offset)) * -1);bottom:calc(var(--os-padding-perpendicular) * -1)}.os-scrollbar-horizontal:hover .os-scrollbar-handle{height:var(--os-handle-perpendicular-size-hover)}.os-scrollbar-horizontal:active .os-scrollbar-handle{height:var(--os-handle-perpendicular-size-active)}.os-scrollbar-vertical{padding:var(--os-padding-axis) var(--os-padding-perpendicular);bottom:var(--os-size);width:var(--os-size)}.os-scrollbar-vertical .os-scrollbar-track:before{left:calc(var(--os-padding-perpendicular) * -1);right:calc(var(--os-padding-perpendicular) * -1)}.os-scrollbar-vertical .os-scrollbar-handle{min-height:var(--os-handle-min-size);max-height:var(--os-handle-max-size);width:var(--os-handle-perpendicular-size);transition:opacity .15s,background-color .15s,border-color .15s,width .15s}.os-scrollbar-vertical .os-scrollbar-handle:before{left:calc((var(--os-padding-perpendicular) + var(--os-handle-interactive-area-offset)) * -1);right:calc(var(--os-padding-perpendicular) * -1)}.os-scrollbar-vertical.os-scrollbar-rtl .os-scrollbar-handle:before{right:calc((var(--os-padding-perpendicular) + var(--os-handle-interactive-area-offset)) * -1);left:calc(var(--os-padding-perpendicular) * -1)}.os-scrollbar-vertical:hover .os-scrollbar-handle{width:var(--os-handle-perpendicular-size-hover)}.os-scrollbar-vertical:active .os-scrollbar-handle{width:var(--os-handle-perpendicular-size-active)}[data-overlayscrollbars-viewport~=measuring]>.os-scrollbar,.os-theme-none.os-scrollbar{display:none!important}.os-theme-dark,.os-theme-light{box-sizing:border-box;--os-size: 10px;--os-padding-perpendicular: 2px;--os-padding-axis: 2px;--os-track-border-radius: 10px;--os-handle-interactive-area-offset: 4px;--os-handle-border-radius: 10px}.os-theme-dark{--os-handle-bg: rgba(0, 0, 0, .44);--os-handle-bg-hover: rgba(0, 0, 0, .55);--os-handle-bg-active: rgba(0, 0, 0, .66)}.os-theme-light{--os-handle-bg: rgba(255, 255, 255, .44);--os-handle-bg-hover: rgba(255, 255, 255, .55);--os-handle-bg-active: rgba(255, 255, 255, .66)}.os-theme-dark{--os-handle-bg: #7c9bb4fa;--os-handle-bg-hover: #2881cafa;--os-handle-bg-active: #2881cafa}h1[data-v-f0a1d45d]{text-align:center;margin-bottom:10px;font-weight:700;color:#0078d7}.changelog[data-v-f0a1d45d]{max-width:700px;margin:0 auto;background:#fff;border-radius:8px;box-shadow:0 2px 10px #0000001a;padding:20px 30px;min-height:150px;max-height:400px;overflow:hidden}.version-block[data-v-f0a1d45d]{border-bottom:1px solid #ddd;padding:15px 0}.version-block[data-v-f0a1d45d]:last-child{border-bottom:none}.version-header[data-v-f0a1d45d]{display:flex;justify-content:space-between;align-items:center;cursor:pointer}.version-title[data-v-f0a1d45d]{font-size:1.2rem;font-weight:600;color:#0078d7}.version-date[data-v-f0a1d45d]{font-size:.9rem;color:#666}.version-content[data-v-f0a1d45d]{margin-top:10px;display:none;color:#444}.version-content ul[data-v-f0a1d45d]{padding-left:20px;margin:0}.version-content ul li[data-v-f0a1d45d]{margin-bottom:6px;line-height:1.4}.version-block.open .version-content[data-v-f0a1d45d]{display:block}.toggle-icon[data-v-f0a1d45d]{font-size:1.2rem;transition:transform .3s ease;user-select:none;color:#0078d7}.version-block.open .toggle-icon[data-v-f0a1d45d]{transform:rotate(48deg)}.changelog-container[data-v-f0a1d45d]{width:40vw;height:55vh}.close-btn[data-v-f0a1d45d]{color:#bbb;position:absolute;right:30px;top:20px;cursor:pointer}
