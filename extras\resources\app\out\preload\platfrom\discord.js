"use strict";(function(){try{var o=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};o.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var o=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new o.Error().stack;e&&(o._sentryDebugIds=o._sentryDebugIds||{},o._sentryDebugIds[e]="35dbd10f-bb94-4c43-ad6c-0f61c734835c",o._sentryDebugIdIdentifier="sentry-dbid-35dbd10f-bb94-4c43-ad6c-0f61c734835c")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});require("electron");require("idb");require("crypto-js");require("dayjs");const d=require("./platfrom.js"),i={userElSelector:'[class="nameTag__37e49 canCopy__37e49"] .panelTitleContainer__37e49',inputElSelector:'div[class="textArea__74017 textAreaSlate__74017 slateContainer_ec4baf"] [contenteditable]',currentSessionUserNameElSelector:" h2 ~ li .selected_bf202d a",messageListElSelector:"ol",unreadElSelector:'[aria-label="私信"] .listItem__650eb .hiddenVisually__27f77',messageElSelector:'[class="markup__75297 messageContent_c19a55"]'};class h extends d.Platform{constructor(e){super(e)}async getUserId(){if(!this.userId){let e=document.querySelector(i.userElSelector);if(e&&(this.userId=e.innerText),this.userId){let t={userId:this.userId,platform:this.platform,phone:this.userId,nickName:this.userName?this.userName:this.userId,session_id:this.viewSessionId};this.sendPlatformUserInfo(t)}}}userId;chatUserId;userName;async init(e){await super.init(e),this.getUserId(),this.bindInputFunction()}_o(e,t){/\/channels\/?/.test(location.pathname)&&(this.bindInputFunction(),this.translateList(),this.getUnreadSessionUserToFans(),this.getUserId(),this.getCurrentSessionUserId())}_u(e){this.getCurrentSessionUserId()}getCurrentSessionUserId(){let e=0;const t=()=>{let n=document.querySelector(i.currentSessionUserNameElSelector);if(n&&e<3){this.chatUserId=n.innerText;let s={mainAccount:this.userId,fansId:this.chatUserId,nickName:this.chatUserId,platform:this.platform};this.sendCurrentSessionFansInfo(s)}else setTimeout(()=>{e++,window.requestAnimationFrame(t)},500)};window.requestAnimationFrame(t)}getUnreadSessionUserToFans(){let e=0;const t=[],n=document.querySelectorAll(i.unreadElSelector).values().toArray();e=n.length,n.forEach(s=>{try{if(s.hasAttribute("aira-isread"))return;{s.setAttribute("aira-isread","true");let a=/.*(?=,)/.exec(s.innerText)[0].trimEnd();t.push({id:a,nickName:a})}}catch(r){console.log("error %s",r.toString())}}),e>0&&this.sendUnReadCount(e),t.length>0&&this.sendNewFansList({viewSessionId:this.viewSessionId,platform:this.platform,mainAccount:this.userId,unreadListInfo:t})}bindInputFunction(){const e=()=>{const t=document.querySelector(i.inputElSelector);if(t){if(t.getAttribute("aria-bind"))return;t.addEventListener("click",n=>{this.changeInputEditStatus(!1,i.inputElSelector),this.createMaskDiv()},!0),t.addEventListener("keydown",n=>{(n.key==="Enter"||n.keyCode===13)&&!n.shiftKey||(this.changeInputEditStatus(!1,i.inputElSelector),this.createMaskDiv())},!0),t.setAttribute("aria-bind","true")}else setTimeout(()=>{window.requestAnimationFrame(e)},500)};window.requestAnimationFrame(e)}sendMessageToInput({message:e,type:t}){this.inputMessage(e,t==="send")}replaceContentEditableText(e,t){const n=window.getSelection();let s;n.rangeCount>0?s=n.getRangeAt(0):(s=document.createRange(),s.selectNodeContents(e)),s.deleteContents();const r=document.createTextNode(t);s.insertNode(r),s.setStartAfter(r),s.collapse(!0),n.removeAllRanges(),n.addRange(s);const a=new InputEvent("beforeinput",{bubbles:!0,cancelable:!0,data:t,inputType:"insertText"});e.dispatchEvent(a);const c=new InputEvent("input",{bubbles:!0,cancelable:!0,data:t,inputType:"insertText"});e.dispatchEvent(c);const l=this.getReactFiber(e);if(l&&l.memoizedProps){if(typeof l.memoizedProps.onBeforeInput=="function"){const u=this.createSyntheticInputEvent("beforeinput",t);l.memoizedProps.onBeforeInput(u)}if(typeof l.memoizedProps.onInput=="function"){const u=this.createSyntheticInputEvent("input",t);l.memoizedProps.onInput(u)}}}createSyntheticInputEvent(e,t){return{type:e,target:{value:t},currentTarget:{textContent:t},data:t,inputType:"insertText",bubbles:!0,cancelable:!0,persist:()=>{},preventDefault:()=>{},stopPropagation:()=>{}}}getReactFiber(e){return e[Object.keys(e).find(t=>t.startsWith("__reactFiber")||t.startsWith("__reactInternalInstance"))]}inputMessage(e,t=!1){return new Promise(async n=>{let s=document.querySelector(i.inputElSelector);if(s){const r=new DataTransfer;r.setData("text/plain",e);const a=new ClipboardEvent("paste",{bubbles:!0,cancelable:!0,clipboardData:r});if(s.dispatchEvent(a),t){const c=new KeyboardEvent("keydown",{key:"Enter",keyCode:13,code:"Enter",bubbles:!0,cancelable:!0,ctrlKey:!1,altKey:!1,shiftKey:!1,metaKey:!1,repeat:!1});Object.assign(c,{isCustom:!0}),setTimeout(()=>{s.dispatchEvent(c),s.blur(),n()},50)}}})}switchShadowState(e){let t=document.querySelector("#myShadow");t&&(e?t.style.display="block":t.style.display="none")}setCaretToEnd(e){const t=document.createRange(),n=window.getSelection();t.selectNodeContents(e),t.collapse(!1),n.removeAllRanges(),n.addRange(t),e.focus()}createMaskDiv(){const e=document.querySelector("#myShadow");if(e)this.setCaretToEnd(e),this.switchShadowState(!0);else{let t=document.querySelector(i.inputElSelector),n=t.cloneNode();n.id="myShadow",n.style.position="absolute",n.style.width="100%",n.style.height="100%",n.style.top="0px",n.style.margin="0px",n.removeAttribute("data-slate-editor"),n.removeAttribute("data-slate-node"),n.style.background="var(--chat-background-default)",n.style.zIndex=9999,n.setAttribute("contenteditable","true");let s=t.closest("[class='textArea__74017 textAreaSlate__74017 slateContainer_ec4baf'] > div");if(s)s.style.position="relative",s.appendChild(n),this.setCaretToEnd(n);else return;n.addEventListener("keydown",async r=>{if((r.key==="Enter"||r.keyCode===13)&&!r.shiftKey){r.stopPropagation(),r.preventDefault(),this.changeInputEditStatus(!1,"#myShadow");let a=await this.translater.translateInput(n.innerText);await this.inputMessage(a||n.innerText,!0),n.innerText="",this.changeInputEditStatus(!0,"#myShadow"),this.changeInputEditStatus(!0,i.inputElSelector),this.switchShadowState(!1)}})}}changeInputEditStatus(e,t){try{let n=document.querySelector(t);if(!n)return;e?n.setAttribute("contenteditable","true"):n.setAttribute("contenteditable","false")}catch(n){console.error(n)}}async translateList(){let e=document.querySelector(i.messageListElSelector);if(e){let t=e.querySelectorAll(i.messageElSelector),n=null;t.forEach(s=>{let r=s.parentNode.querySelector(":scope > img");r&&(n=r.src.match(/(?<=avatars\/)\d*(?=\/)/)[0]!==this.userId?"in":"out"),this.translater.translateMessage(s,{type:n})})}}}exports.DiscordHandler=h;
//# sourceMappingURL=discord.js.map
