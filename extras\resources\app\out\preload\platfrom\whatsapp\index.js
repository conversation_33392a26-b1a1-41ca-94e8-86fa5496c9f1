"use strict";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};e.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},d=new e.Error().stack;d&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[d]="d6f328cd-466e-4ca8-a2de-020ec688b997",e._sentryDebugIdIdentifier="sentry-dbid-d6f328cd-466e-4ca8-a2de-020ec688b997")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const n=require("./whatsapp.js");exports.WhatsappHandler=n.WhatsappHandler;
//# sourceMappingURL=index.js.map
