"use strict";(function(){try{var i=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};i.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var i=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new i.Error().stack;e&&(i._sentryDebugIds=i._sentryDebugIds||{},i._sentryDebugIds[e]="eea9f6a1-4ba6-45f6-8ed3-6434d0c361ad",i._sentryDebugIdIdentifier="sentry-dbid-eea9f6a1-4ba6-45f6-8ed3-6434d0c361ad")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});require("electron");require("idb");require("crypto-js");require("dayjs");const m=require("./platfrom.js");class f extends m.Platform{constructor(e){super(e)}userId;userName;newFansRecord="";chatUserId="";isMac=navigator.userAgentData?.platform==="macOS"||/Mac/.test(navigator.userAgent);scrollTimer=null;scrolling=!1;_o(e,s){this.getUserInfo(),this.getNewFans(),this.scrolling||this.translateList()}_u(e){this.getChatUserId()}sendMessageToInput(e){this.sendMessageEvent({text:e.message,is_send:e.type==="send",input:this.sI.input,btn:this.sI.sendBtn})}getUserInfo(){let e=localStorage.getItem("user_auth");if(e&&!this.userId){e=JSON.parse(e),this.userId=e.id;let s=-1;const t=["tweb","tweb-account-1","tweb-account-2","tweb-account-3","tweb-account-4"],n="users",r=()=>{if(s<t.length-1)s++;else return;const o=t[s],l=indexedDB.open(o);l.onsuccess=u=>{const a=u.target.result;try{if(!a.objectStoreNames.contains(n)){console.warn(`数据库 ${o} 不含 store: ${n}，跳过`),a.close(),r();return}const c=a.transaction([n],"readonly").objectStore(n).get(String(this.userId));c.onsuccess=()=>{if(c.result){const d={userId:this.userId,platform:this.platform,username:c.result.username,nickName:c.result.sortName,phone:c.result.phone,session_id:this.viewSessionId};console.log(`✅ 在 ${o} 中找到用户信息`,d),this.sendPlatformUserInfo(d)}else console.log(`❌ ${o} 中未找到用户`),r();a.close()},c.onerror=d=>{console.error("读取用户失败:",d.target.error),a.close(),r()}}catch(h){console.error("处理过程中出错:",h),a.close(),r()}},l.onerror=u=>{console.error(`❌ 无法打开数据库 ${o}:`,u.target.error),r()}};r()}}getChatUserId(){const e=document.querySelector(this.sI.activeChat);if(e){let s=e.getAttribute("data-peer-id"),t=e.querySelector(".peer-title").innerText;if(s&&this.chatUserId!=s){this.chatUserId=s,console.log("当前正在聊天的用户id切换为：",this.chatUserId),this.bindInputKeydown(),this.setMaskBtn("mask-btn",this.sI.maskDependEl),this.translateList(),this.setScrollEvent();let n={mainAccount:this.userId,fansId:this.chatUserId,nickName:t,platform:this.platform};console.log("当前聊天人信息",n),this.sendCurrentSessionFansInfo(n)}}else this.chatUserId=""}getNewFans(){if(!document.querySelector(".unread"))return;const e=document.querySelectorAll(this.sI.chatList);let s=0;if(e){const t=[];for(let n of e){let r=n.getAttribute("data-peer-id");const o=n.querySelector(".unread");if(o&&o.querySelector(".verified-icon"))continue;if((o?o.innerText:0)>0&&r>0){s+=1;let u={id:r,nickName:n.querySelector(this.sI.newFansNickName).innerText};this.newFansRecord.indexOf(r)===-1&&(this.newFansRecord+=`,${r}`,t.push(u))}}console.log("sendUnreadCount",s),this.sendUnReadCount(s),t.length>0&&this.sendNewFansList({viewSessionId:this.viewSessionId,platform:this.platform,mainAccount:this.userId,unreadListInfo:t})}}bindInputKeydown(){const e=()=>{const s=document.querySelector(this.sI.input);console.log("bindInputKeydown=====",s),s?s.addEventListener("keydown",async t=>{this.setMaskBtn("mask-btn",this.sI.maskDependEl),(t.key==="Enter"||t.keyCode===13)&&!t.shiftKey&&(t.preventDefault(),t.stopPropagation(),t.stopImmediatePropagation(),this.sendMessage())},!0):setTimeout(()=>{window.requestAnimationFrame(e)},500)};window.requestAnimationFrame(e)}async sendMessage(){let s=document.querySelector(this.sI.input).innerText;const t=this.translater?.config,n=t&&t.trans_over;if(s.trim()&&n){this.clearInput(this.sI.input);let r=await this.translater.translateInput(s);this.sendMessageEvent({text:r||s,is_send:!!r,input:this.sI.input,btn:this.sI.sendBtn})}else document.querySelector(this.sI.sendBtn).click()}async translateList(){let e=document.querySelector(this.sI.messageListElSelector);if(e){const s=e.querySelectorAll(this.sI.reciveMessageElSelector),t=e.querySelectorAll(this.sI.sendMessageElSelector),n=s.length+t.length;if(n==0)return;document.querySelectorAll("[data-translated]").length>=n;const r=()=>{document.querySelectorAll("[data-translated]").length>=n&&this.scrollToBottom()};s.forEach(o=>{const l=this.setOutMsgDom(o);l&&this.translater.translateMessage(l,{callback:r,type:"in"})}),t.forEach(o=>{this.translater.translateMessage(o,{callback:r,type:"out"})})}}scrollToBottom(){let e=document.querySelector(this.sI.scrollContent);if(e){var s=e.scrollHeight-e.clientHeight;s-e.scrollTop<950&&(e.scrollTop=s)}else console.error("未找到可滚动的元素")}setScrollEvent(){const e=document.querySelector(this.sI.scrollContent);e?e.addEventListener("scroll",()=>{this.scrolling=!0,this.scrollTimer&&clearTimeout(this.scrollTimer),this.scrollTimer=setTimeout(()=>{this.scrolling=!1,this.translateList()},500)}):setTimeout(()=>{this.setScrollEvent()},500)}clearInput(e){let s=document.querySelector(e);s&&(s.innerHTML="")}setEditable(e,s){let t=document.querySelector(e);s?(t.removeAttribute("contenteditable"),t.setAttribute("contenteditable","true")):(t.removeAttribute("contenteditable"),t.setAttribute("contenteditable","false"))}setMaskBtn(e,s){if(document.querySelector("#"+e))setTimeout(()=>{this.setMaskBtn(e,s)},500);else{const t=document.createElement("div");t.id=e;const n=document.querySelector(s);n&&(n.style.position="relative",t.style.position="absolute",t.style.top="0",t.style.left="0",t.style.width="100%",t.style.height="100%",t.style.zIndex="999",n.appendChild(t),t.addEventListener("click",async r=>{r.stopPropagation(),this.sendMessage()}))}}sendMessageEvent(e){let s=document.querySelector(e.input);s.focus(),s.innerText=e.text,e.is_send&&setTimeout(()=>{document.querySelector(e.btn)?.click(),setTimeout(()=>{this.scrollToBottom()},1e3)},50)}setOutMsgDom(e){let s=e.querySelector(".blueglob-msg");if(s)return s;const t=e.firstChild;if(t.nodeType===Node.TEXT_NODE){const n=t.textContent.trim(),r=document.createElement("span");return r.className="blueglob-msg",r.textContent=n,e.replaceChild(r,t),e.querySelector(".blueglob-msg")}else return t}}exports.TelegramHandler=f;
//# sourceMappingURL=telegram.js.map
