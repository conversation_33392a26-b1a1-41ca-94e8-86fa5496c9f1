// import {useState} from 'react';
// import logo from './assets/images/logo-universal.png';
// import './App.css';
// import {Greet} from "../wailsjs/go/main/App";

// function App() {
//     const [resultText, setResultText] = useState("Please enter your name below 👇");
//     const [name, setName] = useState('');
//     const updateName = (e: any) => setName(e.target.value);
//     const updateResultText = (result: string) => setResultText(result);

//     function greet() {
//         Greet(name).then(updateResultText);
//     }

//     return (
//         <div id="App">
//             <img src={logo} id="logo" alt="logo"/>
//             <div id="result" className="result">{resultText}</div>
//             <div id="input" className="input-box">
//                 <input id="name" className="input" onChange={updateName} autoComplete="off" name="input" type="text"/>
//                 <button className="btn" onClick={greet}>Greet</button>
//             </div>
//         </div>
//     )
// }

// export default App


import React, { useEffect, useState } from "react";
import { GetMode, StartUninstall, Install, Uninstall } from "../wailsjs/go/main/App";

function App() {
    const [mode, setMode] = useState("install");
    const [progress, setProgress] = useState(0);

    useEffect(() => {
        async function init() {
            const m = await GetMode();
            setMode(m);

            if (m === "uninstall") {
                const steps = await StartUninstall();
                for (let i = 0; i < steps.length; i++) {
                    setProgress(steps[i]);
                    // 这里的延时和后端保持一致（500ms），避免 UI 一下子跳满
                    await new Promise((r) => setTimeout(r, 500));
                }
            }
        }
        init();
    }, []);

    const install = () => {
        Install();
    }
    const uninstall = () => {
        Uninstall();
    }

    return (
        <div style={styles.container}>
            {mode === "install" ? (
                <>
                    <h1>欢迎使用安装程序</h1>
                    <button onClick={install}>
                        install
                    </button>
                </>
            ) : (
                <>
                    <h1>卸载程序:正在卸载...</h1>
                    <button onClick={uninstall}>
                        uninstall
                    </button>
                    <div style={styles.progressBar}>
                        <div style={{ ...styles.progress, width: `${progress}%` }}></div>
                    </div>
                    <p>{progress}%</p>
                </>
            )}
        </div>
    );
}

const styles = {
    container: {
        padding: "20px",
        fontFamily: "Arial, sans-serif",
    },
    progressBar: {
        width: "100%",
        height: "20px",
        background: "#eee",
        borderRadius: "10px",
        overflow: "hidden",
    },
    progress: {
        height: "100%",
        background: "#4caf50",
        transition: "width 0.5s ease",
    },
};

export default App;

