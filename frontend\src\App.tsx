// import {useState} from 'react';
// import logo from './assets/images/logo-universal.png';
// import './App.css';
// import {Greet} from "../wailsjs/go/main/App";

// function App() {
//     const [resultText, setResultText] = useState("Please enter your name below 👇");
//     const [name, setName] = useState('');
//     const updateName = (e: any) => setName(e.target.value);
//     const updateResultText = (result: string) => setResultText(result);

//     function greet() {
//         Greet(name).then(updateResultText);
//     }

//     return (
//         <div id="App">
//             <img src={logo} id="logo" alt="logo"/>
//             <div id="result" className="result">{resultText}</div>
//             <div id="input" className="input-box">
//                 <input id="name" className="input" onChange={updateName} autoComplete="off" name="input" type="text"/>
//                 <button className="btn" onClick={greet}>Greet</button>
//             </div>
//         </div>
//     )
// }

// export default App


import React, { useEffect, useState } from "react";
import { GetMode, StartUninstall, Install, Uninstall } from "../wailsjs/go/main/App";
import { EventsOn } from "../wailsjs/runtime/runtime";
import './App.css';

interface UninstallProgressData {
    step: string;
    progress: number;
}

function App() {
    const [mode, setMode] = useState("install");
    const [progress, setProgress] = useState(0);
    const [currentStep, setCurrentStep] = useState("");
    const [isUninstalling, setIsUninstalling] = useState(false);
    const [uninstallComplete, setUninstallComplete] = useState(false);
    const [error, setError] = useState("");

    useEffect(() => {
        async function init() {
            const m = await GetMode();
            setMode(m);

            // 监听卸载进度事件
            EventsOn("uninstall-progress", (data: UninstallProgressData) => {
                setProgress(data.progress);
                setCurrentStep(data.step);
                if (data.progress === 100) {
                    setUninstallComplete(true);
                    setTimeout(() => {
                        // 卸载完成后的延迟，让用户看到完成状态
                    }, 2000);
                }
            });

            // 监听卸载错误事件
            EventsOn("uninstall-error", (errorMsg: string) => {
                setError(errorMsg);
                setIsUninstalling(false);
            });

            // 如果是卸载模式，自动开始卸载
            if (m === "uninstall") {
                setIsUninstalling(true);
                try {
                    // 使用模拟进度更新，然后执行真正的卸载
                    const steps = await StartUninstall();
                    for (let i = 0; i < steps.length; i++) {
                        setProgress(steps[i]);
                        setCurrentStep(`正在卸载... ${steps[i]}%`);
                        await new Promise((r) => setTimeout(r, 500));
                    }
                    setUninstallComplete(true);

                    // 显示完成消息2秒后执行真正的卸载
                    setTimeout(async () => {
                        try {
                            await Uninstall(); // 这会执行真正的卸载并关闭程序
                        } catch (err) {
                            console.error("卸载失败:", err);
                        }
                    }, 2000);
                } catch (err) {
                    setError("启动卸载失败: " + err);
                    setIsUninstalling(false);
                }
            }
        }
        init();
    }, []);

    const handleInstall = async () => {
        try {
            const result = await Install();
            if (result) {
                alert("安装成功！");
            } else {
                alert("安装失败！");
            }
        } catch (err) {
            alert("安装出错: " + err);
        }
    };

    const handleManualUninstall = async () => {
        if (confirm("确定要卸载此程序吗？")) {
            try {
                await Uninstall();
            } catch (err) {
                alert("卸载出错: " + err);
            }
        }
    };

    return (
        <div className="app-container">
            {mode === "install" ? (
                <div className="install-container">
                    <div className="header">
                        <div className="app-icon">📦</div>
                        <h1 className="app-title">应用安装程序</h1>
                        <p className="app-description">欢迎使用我们的应用程序安装向导</p>
                    </div>

                    <div className="install-content">
                        <div className="feature-list">
                            <div className="feature-item">
                                <span className="feature-icon">✅</span>
                                <span>快速安装</span>
                            </div>
                            <div className="feature-item">
                                <span className="feature-icon">🔒</span>
                                <span>安全可靠</span>
                            </div>
                            <div className="feature-item">
                                <span className="feature-icon">🚀</span>
                                <span>高性能</span>
                            </div>
                        </div>

                        <button className="install-button" onClick={handleInstall}>
                            <span className="button-icon">⬇️</span>
                            开始安装
                        </button>
                    </div>
                </div>
            ) : (
                <div className="uninstall-container">
                    <div className="header">
                        <div className="app-icon uninstall-icon">🗑️</div>
                        <h1 className="app-title">卸载程序</h1>
                        <p className="app-description">
                            {uninstallComplete ? "卸载已完成" : "正在卸载应用程序..."}
                        </p>
                    </div>

                    {error ? (
                        <div className="error-container">
                            <div className="error-icon">❌</div>
                            <p className="error-message">{error}</p>
                            <button className="retry-button" onClick={handleManualUninstall}>
                                重试卸载
                            </button>
                        </div>
                    ) : (
                        <div className="progress-container">
                            <div className="progress-info">
                                <span className="progress-text">{currentStep || "准备卸载..."}</span>
                                <span className="progress-percentage">{progress}%</span>
                            </div>

                            <div className="progress-bar">
                                <div
                                    className="progress-fill"
                                    style={{ width: `${progress}%` }}
                                >
                                    <div className="progress-shine"></div>
                                </div>
                            </div>

                            <div className="progress-steps">
                                {[
                                    { name: "准备", progress: 10 },
                                    { name: "清理", progress: 40 },
                                    { name: "删除", progress: 70 },
                                    { name: "完成", progress: 100 }
                                ].map((step, index) => (
                                    <div
                                        key={index}
                                        className={`step ${progress >= step.progress ? 'completed' : ''}`}
                                    >
                                        <div className="step-circle">
                                            {progress >= step.progress ? '✓' : index + 1}
                                        </div>
                                        <span className="step-name">{step.name}</span>
                                    </div>
                                ))}
                            </div>

                            {uninstallComplete && (
                                <div className="completion-message">
                                    <div className="success-icon">🎉</div>
                                    <p>卸载成功完成！程序将在几秒后自动关闭。</p>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}

export default App;

