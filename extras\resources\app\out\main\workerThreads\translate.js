"use strict";
!function() {
  try {
    var e = "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof globalThis ? globalThis : "undefined" != typeof self ? self : {};
    e.SENTRY_RELEASE = { id: "2.1.3" };
  } catch (e2) {
  }
}();
;
{
  try {
    (function() {
      var e = "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof globalThis ? globalThis : "undefined" != typeof self ? self : {}, n = new e.Error().stack;
      n && (e._sentryDebugIds = e._sentryDebugIds || {}, e._sentryDebugIds[n] = "f78eb5de-425d-4f09-910a-7e71e1643f87", e._sentryDebugIdIdentifier = "sentry-dbid-f78eb5de-425d-4f09-910a-7e71e1643f87");
    })();
  } catch (e) {
  }
}
;
const { parentPort } = require("worker_threads");
const { googleAsyncTranslate } = require("../utils/googleTranslateApi.js");
parentPort.on("message", (params) => {
  console.log("translate", params);
  try {
    googleAsyncTranslate(params).then((res) => {
      parentPort.postMessage({
        id: params.id,
        result: res
      });
    });
  } catch (error) {
    parentPort.emit("messageerror", error);
  }
});
//# sourceMappingURL=translate.js.map
