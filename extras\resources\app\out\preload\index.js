"use strict";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};e.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},o=new e.Error().stack;o&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[o]="8f173e9e-c7d4-492d-ac56-92fd64002614",e._sentryDebugIdIdentifier="sentry-dbid-8f173e9e-c7d4-492d-ac56-92fd64002614")})()}catch{}const l=require("electron"),i=require("@electron-toolkit/preload"),c=require("./tanslate/index.js"),f=require("./platfrom/platfrom.js"),m=require("@sentry/electron/renderer"),p=require("./shared/sentry-config.js");m.init(p.getPreloadConfig());const u={facebook:()=>Promise.resolve().then(()=>require("./platfrom/facebook.js")).then(e=>e.FacebookHandler),whatsapp:()=>Promise.resolve().then(()=>require("./platfrom/whatsapp/index.js")).then(e=>e.WhatsappHandler),telegram:()=>Promise.resolve().then(()=>require("./platfrom/telegram.js")).then(e=>e.TelegramHandler),instagram:()=>Promise.resolve().then(()=>require("./platfrom/instagram.js")).then(e=>e.InstagramHandler),tiktok:()=>Promise.resolve().then(()=>require("./platfrom/tiktok.js")).then(e=>e.TiktokHandler),facebookBusiness:()=>Promise.resolve().then(()=>require("./platfrom/facebookBusiness.js")).then(e=>e.FacebookBusinessHandler),twitter:()=>Promise.resolve().then(()=>require("./platfrom/twitter.js")).then(e=>e.TwitterHandler),discord:()=>Promise.resolve().then(()=>require("./platfrom/discord.js")).then(e=>e.DiscordHandler)},n={startTime:performance.now(),mark(e){const o=performance.now();console.log(`[Preload Performance] ${e}: ${(o-this.startTime).toFixed(2)}ms`),this.startTime=o}};try{let e=null;const o=()=>{n.mark("Start Electron API init");const r={isWin:process.platform==="win32",isMac:process.platform==="darwin",isLinux:process.platform==="linux"};process.contextIsolated?(l.contextBridge.exposeInMainWorld("electron",{...i.electronAPI,ipcRenderer:{...i.electronAPI.ipcRenderer,send:(...t)=>{i.electronAPI.ipcRenderer.send(...t)},invoke:(...t)=>i.electronAPI.ipcRenderer.invoke(...t)}}),l.contextBridge.exposeInMainWorld("platform",r)):(window.electron=i.electronAPI,window.platform=r),n.mark("Electron API initialized")},s=async()=>{n.mark("Start platform detection");const r=f.getPlatform();if(!r){console.warn("[Preload] No platform detected");return}n.mark(`Platform detected: ${r}`);const t=u[r];if(!t){console.error(`[Preload] No handler found for platform: ${r}`);return}try{const a=await t();n.mark(`Handler loaded for ${r}`),e=new a(r);const d=new c.Translater;await e.init(d),n.mark(`Platform handler initialized for ${r}`)}catch(a){console.error(`[Preload] Failed to load handler for ${r}:`,a)}};(async()=>{n.mark("Preload initialization started"),o(),await s(),n.mark("Preload initialization completed")})()}catch(e){console.error("[Preload] Initialization error:",e)}
//# sourceMappingURL=index.js.map
