"use strict";(function(){try{var s=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};s.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var s=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new s.Error().stack;e&&(s._sentryDebugIds=s._sentryDebugIds||{},s._sentryDebugIds[e]="ec85789e-e86c-49b8-a899-2afe57ac8cf0",s._sentryDebugIdIdentifier="sentry-dbid-ec85789e-e86c-49b8-a899-2afe57ac8cf0")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const h=(s,e,t={trailing:!0,useRAF:!1})=>{let r=0,i=null,u=null,o=null;const n=()=>{r=Date.now(),i=null,s.apply(o,u)};return function(...a){const c=Date.now(),l=e-(c-r);l<=0||r===0?(i&&(clearTimeout(i),i=null),r=c,s.apply(this,a)):t.trailing&&!i&&(u=a,o=this,i=setTimeout(n,l))}},m=function(e,t,r=!1){let i;return function(...o){const n=this,a=()=>{i=null,r||e.apply(n,o)},c=r&&!i;clearTimeout(i),i=setTimeout(a,t),c&&e.apply(n,o)}};class d{constructor(){this.updates=[],this.rafId=null}add(e){this.updates.push(e),this.scheduleUpdate()}scheduleUpdate(){this.rafId||(this.rafId=requestAnimationFrame(()=>{this.flush()}))}flush(){const e=this.updates.splice(0);this.rafId=null,e.forEach(t=>{try{t()}catch(r){console.error("[BatchDOMUpdater] Update failed:",r)}})}clear(){this.updates=[],this.rafId&&(cancelAnimationFrame(this.rafId),this.rafId=null)}}class p{ObserveSet=new Set;constructor(e,t={}){this.callback=e,this.options={throttleDelay:100,batchSize:50,...t},this.observer=null,this.mutationQueue=[],this.isProcessing=!1,this.throttledProcess=h(this.processMutations.bind(this),this.options.throttleDelay,{trailing:!0})}observe(e,t){this.observer||(this.observer=new MutationObserver(r=>{this.mutationQueue.push(...r),this.throttledProcess()})),this.ObserveSet.has(e)||(this.ObserveSet.add(e),this.observer.observe(e,t))}processMutations(){if(!(this.isProcessing||this.mutationQueue.length===0)){this.isProcessing=!0;try{const e=this.mutationQueue.splice(0,this.options.batchSize);this.callback(e,this.observer)}catch(e){console.error("[OptimizedMutationObserver] Processing error:",e)}finally{this.isProcessing=!1,this.mutationQueue.length>0&&setTimeout(()=>this.processMutations(),0)}}}disconnect(){this.observer&&(this.observer.disconnect(),this.observer=null),this.mutationQueue=[],this.isProcessing=!1}}class f{constructor(e="Unknown"){this.name=e,this.marks=new Map,this.measures=new Map}mark(e){const t=`${this.name}-${e}`;return performance.mark(t),this.marks.set(e,performance.now()),this}measure(e,t=null){const r=`${this.name}-${e}-${t||"now"}`;t?performance.measure(r,`${this.name}-${e}`,`${this.name}-${t}`):performance.measure(r,`${this.name}-${e}`);const i=performance.getEntriesByName(r)[0];return this.measures.set(`${e}-${t||"now"}`,i.duration),i.duration}getResults(){return{marks:Object.fromEntries(this.marks),measures:Object.fromEntries(this.measures)}}report(){console.group(`[Performance Report] ${this.name}`),console.log("Marks:",this.marks),console.log("Measures:",this.measures);const e=Array.from(this.measures.values()).reduce((t,r)=>t+r,0);console.log(`Total Time: ${e.toFixed(2)}ms`),console.groupEnd()}clear(){this.marks.clear(),this.measures.clear(),performance.clearMarks(),performance.clearMeasures()}}const b=s=>new f(s);exports.BatchDOMUpdater=d;exports.OptimizedMutationObserver=p;exports.PerformanceMonitor=f;exports.createPerformanceMonitor=b;exports.debounce=m;exports.throttle=h;
//# sourceMappingURL=performance.js.map
