package backend

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"syscall"
)

func createSelfDeleteBatch(exePath string) (string, error) {
	batPath := filepath.Join(os.TempDir(), "delete_self.bat")
	batContent := fmt.Sprintf(`@echo off
:loop
del "%s" >nul 2>&1
if exist "%s" (
    timeout /t 1 /nobreak >nul
    goto loop
)
del "%%~f0" >nul 2>&1`, exePath, exePath)

	err := os.WriteFile(batPath, []byte(batContent), 0644)
	if err != nil {
		return "", err
	}
	return batPath, nil
}
func runHiddenBatch(batPath string) error {
	cmd := exec.Command("cmd", "/C", batPath)
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow: true,
	}
	return cmd.Start()
}

func UninstallApp() (string, error) {
	exePath, _ := os.Executable()

	// 删除安装目录下其他文件
	os.RemoveAll(InstallDir) // 或逐个删除，不保留 installer

	// 删除注册表、快捷方式等
	RemoveUninstallInfo(AppName)

	// 生成批处理并启动
	batPath, err := createSelfDeleteBatch(exePath)
	if err != nil {
		return "", err
	}
	runHiddenBatch(batPath)

	// 退出程序
	os.Exit(0)

	return "卸载已启动，安装目录及安装器将在几秒后删除", nil
}
