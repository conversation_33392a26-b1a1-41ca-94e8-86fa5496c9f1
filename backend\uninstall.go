package backend

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"syscall"
	"time"
)

// UninstallProgress 卸载进度回调函数类型
type UninstallProgress func(step string, progress int)

// UninstallStep 卸载步骤
type UninstallStep struct {
	Name        string
	Description string
	Progress    int
}

// 定义卸载步骤
var uninstallSteps = []UninstallStep{
	{"准备卸载", "正在准备卸载程序...", 10},
	{"删除快捷方式", "正在删除快捷方式...", 25},
	{"删除注册表", "正在清理注册表信息...", 40},
	{"删除文件", "正在删除程序文件...", 70},
	{"清理临时文件", "正在清理临时文件...", 85},
	{"完成卸载", "卸载完成", 100},
}

func createSelfDeleteBatch(exePath string) (string, error) {
	batPath := filepath.Join(os.TempDir(), "delete_self.bat")
	batContent := fmt.Sprintf(`@echo off
:loop
del "%s" >nul 2>&1
if exist "%s" (
    timeout /t 1 /nobreak >nul
    goto loop
)
del "%%~f0" >nul 2>&1`, exePath, exePath)

	err := os.WriteFile(batPath, []byte(batContent), 0644)
	if err != nil {
		return "", err
	}
	return batPath, nil
}

func runHiddenBatch(batPath string) error {
	cmd := exec.Command("cmd", "/C", batPath)
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow: true,
	}
	return cmd.Start()
}

// UninstallAppWithProgress 带进度回调的卸载函数
func UninstallAppWithProgress(progressCallback UninstallProgress) error {
	log.Printf("开始卸载应用: %s", AppName)

	for _, step := range uninstallSteps {
		if progressCallback != nil {
			progressCallback(step.Description, step.Progress)
		}

		// 模拟每个步骤的执行时间
		time.Sleep(500 * time.Millisecond)

		switch step.Name {
		case "准备卸载":
			log.Printf("准备卸载 %s", AppName)

		case "删除快捷方式":
			if err := removeShortcuts(); err != nil {
				log.Printf("删除快捷方式失败: %v", err)
				// 继续执行，不中断卸载过程
			}

		case "删除注册表":
			if err := RemoveUninstallInfo(AppName); err != nil {
				log.Printf("删除注册表信息失败: %v", err)
				// 继续执行，不中断卸载过程
			}

		case "删除文件":
			if err := removeInstallFiles(); err != nil {
				log.Printf("删除文件失败: %v", err)
				// 继续执行，不中断卸载过程
			}

		case "清理临时文件":
			if err := cleanupTempFiles(); err != nil {
				log.Printf("清理临时文件失败: %v", err)
				// 继续执行，不中断卸载过程
			}

		case "完成卸载":
			log.Printf("卸载完成")
		}
	}

	return nil
}

// removeShortcuts 删除快捷方式
func removeShortcuts() error {
	// 删除安装目录中的快捷方式
	lnkPath := filepath.Join(InstallDir, "卸载 "+AppName+".lnk")
	if err := os.Remove(lnkPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("删除快捷方式失败: %v", err)
	}
	return nil
}

// removeInstallFiles 删除安装文件
func removeInstallFiles() error {
	if err := os.RemoveAll(InstallDir); err != nil {
		return fmt.Errorf("删除安装目录失败: %v", err)
	}
	return nil
}

// cleanupTempFiles 清理临时文件
func cleanupTempFiles() error {
	// 清理可能的临时文件
	tempPattern := filepath.Join(os.TempDir(), AppName+"*")
	matches, err := filepath.Glob(tempPattern)
	if err != nil {
		return err
	}

	for _, match := range matches {
		if err := os.RemoveAll(match); err != nil {
			log.Printf("清理临时文件失败 %s: %v", match, err)
		}
	}
	return nil
}

// UninstallApp 原有的卸载函数，保持兼容性
func UninstallApp() (string, error) {
	log.Printf("开始执行卸载...")

	exePath, _ := os.Executable()

	// 删除安装目录下其他文件
	log.Printf("删除安装目录: %s", InstallDir)
	os.RemoveAll(InstallDir)

	// 删除注册表、快捷方式等
	log.Printf("删除注册表信息")
	RemoveUninstallInfo(AppName)

	// 生成批处理并启动
	log.Printf("创建自删除批处理")
	batPath, err := createSelfDeleteBatch(exePath)
	if err != nil {
		log.Printf("创建批处理失败: %v", err)
		return "", err
	}

	log.Printf("启动自删除批处理: %s", batPath)
	runHiddenBatch(batPath)

	// 延迟退出，确保批处理有时间启动
	log.Printf("程序即将退出...")
	go func() {
		time.Sleep(1 * time.Second)
		os.Exit(0)
	}()

	return "卸载已启动，安装目录及安装器将在几秒后删除", nil
}

// StartUninstallProcess 启动卸载进程（用于前端调用）
func StartUninstallProcess(progressCallback UninstallProgress) error {
	// 执行卸载步骤
	if err := UninstallAppWithProgress(progressCallback); err != nil {
		return err
	}

	// 延迟一秒后执行自删除
	time.Sleep(1 * time.Second)

	// 获取当前执行文件路径
	exePath, err := os.Executable()
	if err != nil {
		return fmt.Errorf("获取执行文件路径失败: %v", err)
	}

	// 生成批处理并启动自删除
	batPath, err := createSelfDeleteBatch(exePath)
	if err != nil {
		return fmt.Errorf("创建自删除批处理失败: %v", err)
	}

	if err := runHiddenBatch(batPath); err != nil {
		return fmt.Errorf("启动自删除批处理失败: %v", err)
	}

	// 退出程序
	go func() {
		time.Sleep(2 * time.Second)
		os.Exit(0)
	}()

	return nil
}
