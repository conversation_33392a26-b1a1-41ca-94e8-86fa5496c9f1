import{u as j,r as p,o as G,w as W,f as d,h as s,j as m,ab as D,ac as P,F as y,B as w,i as F,C as z,al as $,D as T,n as E,E as q,O as e2,m as l2,p as c,A as h,a4 as R,y as L}from"./index-BO8ZgokY.js";(function(){try{var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};r.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},f=new r.Error().stack;f&&(r._sentryDebugIds=r._sentryDebugIds||{},r._sentryDebugIds[f]="b51b931f-479f-446f-8c40-e549a0efd0cb",r._sentryDebugIdIdentifier="sentry-dbid-b51b931f-479f-446f-8c40-e549a0efd0cb")})()}catch{}const t2={class:"sys_base"},s2={class:"quick_cont"},a2={class:"cont_left"},o2={class:"search"},n2={class:"menu_btn"},i2=["src"],c2={class:"input"},u2={class:"teamList"},C2={class:"items"},d2={key:0,class:"name_son"},r2=["onClick"],g2=["onClick"],m2=["src"],v2=["onClick"],p2=["src"],h2={class:"item"},f2=["onClick"],A2=["onClick"],B2=["src"],k2=["onClick"],y2=["src"],w2={class:"cont_right"},E2={class:"right_header"},I2=["src"],S2={class:"quick_message"},Q2={class:"item"},U2={class:"input"},F2=["onClick"],T2=["src"],R2={__name:"quick",setup(r){const f=j();e2();const Y=l2(),I=new URL("data:image/svg+xml,%3csvg%20width='24'%20height='24'%20viewBox='0%200%2024%2024'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M21.6691%207.91496C21.1405%206.66525%2020.384%205.54304%2019.4205%204.57952C18.457%203.61601%2017.3347%202.85945%2016.085%202.33086C14.7908%201.78343%2013.4164%201.50586%2012%201.50586C10.5836%201.50586%209.20926%201.78343%207.91502%202.33084C6.66531%202.85942%205.5431%203.61598%204.57958%204.5795C3.61607%205.54302%202.85951%206.66523%202.33092%207.91494C1.78349%209.2092%201.50592%2010.5836%201.50592%2012C1.50592%2013.4163%201.78349%2014.7907%202.3309%2016.0849C2.85948%2017.3347%203.61605%2018.4569%204.57956%2019.4204C5.54308%2020.3839%206.66529%2021.1405%207.915%2021.6691C9.20924%2022.2165%2010.5836%2022.494%2012%2022.494C13.4163%2022.494%2014.7907%2022.2165%2016.085%2021.6691C17.3347%2021.1405%2018.4569%2020.3839%2019.4204%2019.4204C20.384%2018.4569%2021.1405%2017.3347%2021.6691%2016.0849C22.2165%2014.7907%2022.4941%2013.4163%2022.4941%2012C22.4941%2010.5836%2022.2165%209.2092%2021.6691%207.91496ZM12%2021.0059C7.03415%2021.0059%202.99411%2016.9658%202.99411%2012C2.99411%207.03409%207.03415%202.99405%2012%202.99405C16.9659%202.99405%2021.0059%207.03409%2021.0059%2012C21.0059%2016.9658%2016.9659%2021.0059%2012%2021.0059Z'%20fill='%235577FF'/%3e%3cpath%20d='M17.3438%2011.25H12.75V6.65625C12.75%206.29381%2012.4142%206%2012%206C11.5858%206%2011.25%206.29381%2011.25%206.65625V11.25H6.65625C6.29381%2011.25%206%2011.5858%206%2012C6%2012.4142%206.29381%2012.75%206.65625%2012.75H11.25V17.3438C11.25%2017.7062%2011.5858%2018%2012%2018C12.4142%2018%2012.75%2017.7062%2012.75%2017.3438V12.75H17.3438C17.7062%2012.75%2018%2012.4142%2018%2012C18%2011.5858%2017.7062%2011.25%2017.3438%2011.25Z'%20fill='%235577FF'/%3e%3c/svg%3e",import.meta.url).href;new URL(""+new URL("icon55-B3hVUdeG.svg",import.meta.url).href,import.meta.url).href,new URL("data:image/svg+xml,%3csvg%20width='18'%20height='17'%20viewBox='0%200%2018%2017'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M11.565%2011.2548C11.565%2011.2548%2010.846%2010.7711%2010.0545%2011.5793C10.0545%2011.5793%209.40274%2012.3033%208.68364%2011.3801C8.68364%2011.3801%206.69651%209.19828%206.08468%206.75929C6.08468%206.75929%205.9361%206.32936%206.49676%206.24011C6.49676%206.24011%208.00704%205.91583%208.1886%205.23447L7.28726%200.609961C7.28726%200.609961%207.01509%20-0.233749%205.95905%200.252621C5.95905%200.252621%204.15501%200.713491%203.36451%201.48835L3.21797%201.82104L3.05664%203.12375C3.05664%203.12375%203.1413%2011.9402%2010.7741%2016.8844C10.7741%2016.8844%2013.3877%2017.6882%2014.8222%2015.0529L15.0046%2014.3715L11.565%2011.2548Z'%20fill='%232072F7'/%3e%3c/svg%3e",import.meta.url).href,new URL("data:image/svg+xml,%3csvg%20width='17'%20height='17'%20viewBox='0%200%2017%2017'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M15.4%204H1.6C1.52121%204%201.44319%204.01617%201.37039%204.04758C1.29759%204.07898%201.23145%204.12502%201.17574%204.18306C1.12002%204.24109%201.07583%204.30999%201.04567%204.38582C1.01552%204.46165%201%204.54292%201%204.625V13.375C1%2013.7201%201.26938%2014%201.6%2014H15.4C15.5591%2014%2015.7117%2013.9342%2015.8243%2013.8169C15.9368%2013.6997%2016%2013.5408%2016%2013.375V4.625C16%204.45924%2015.9368%204.30027%2015.8243%204.18306C15.7117%204.06585%2015.5591%204%2015.4%204ZM13.9606%2012.4655C13.9191%2012.5094%2013.8695%2012.5442%2013.8149%2012.568C13.7603%2012.5918%2013.7017%2012.6041%2013.6425%2012.6041C13.5833%2012.6041%2013.5247%2012.5918%2013.4701%2012.568C13.4155%2012.5442%2013.3659%2012.5094%2013.3244%2012.4655L10.2931%209.30859L8.80625%2010.7337C8.72332%2010.8136%208.61452%2010.858%208.50156%2010.858C8.38861%2010.858%208.2798%2010.8136%208.19687%2010.7337L6.75%209.34831L3.7575%2012.4661C3.71584%2012.5099%203.66628%2012.5446%203.61168%2012.5683C3.55708%2012.592%203.49852%2012.6042%203.43937%2012.6042C3.38023%2012.6042%203.32167%2012.592%203.26707%2012.5683C3.21247%2012.5446%203.16291%2012.5099%203.12125%2012.4661C3.0369%2012.3782%202.98952%2012.259%202.98952%2012.1348C2.98952%2012.0105%203.0369%2011.8913%203.12125%2011.8034L6.08687%208.71289L3.09375%205.84635C3.04852%205.80521%203.01165%205.75505%202.98532%205.69883C2.95899%205.64261%202.94372%205.58147%202.94043%205.51901C2.93714%205.45655%202.94588%205.39403%202.96613%205.33514C2.98639%205.27626%203.01777%205.22219%203.0584%205.17613C3.09903%205.13007%203.1481%205.09294%203.20272%205.06694C3.25734%205.04094%203.3164%205.0266%203.37642%205.02474C3.43645%205.02289%203.49622%205.03357%203.55223%205.05616C3.60823%205.07874%203.65932%205.11278%203.7025%205.15625L8.5%209.75195L13.2956%205.15625C13.3387%205.11262%2013.3898%205.07842%2013.4458%205.05569C13.5018%205.03296%2013.5616%205.02214%2013.6217%205.02389C13.6818%205.02563%2013.741%205.0399%2013.7957%205.06584C13.8504%205.09179%2013.8995%205.12889%2013.9403%205.17496C13.981%205.22102%2014.0124%205.27512%2014.0327%205.33405C14.053%205.39298%2014.0618%205.45556%2014.0585%205.51808C14.0552%205.5806%2014.04%205.64181%2014.0136%205.69808C13.9872%205.75435%2013.9503%205.80454%2013.905%205.8457L10.955%208.67253L13.9594%2011.8021C14.0013%2011.8455%2014.0346%2011.8972%2014.0574%2011.954C14.0801%2012.0109%2014.0919%2012.0719%2014.092%2012.1335C14.0922%2012.1951%2014.0806%2012.2562%2014.0581%2012.3131C14.0355%2012.3701%2014.0024%2012.4219%2013.9606%2012.4655Z'%20fill='%232072F7'/%3e%3c/svg%3e",import.meta.url).href;const x=new URL("data:image/svg+xml,%3csvg%20width='20'%20height='21'%20viewBox='0%200%2020%2021'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M2.49499%202.43631L1.98999%206.47881H2.49499C2.49499%205.06381%203.60624%203.95256%205.02124%203.95256H8.55874V16.8363C8.55874%2017.5438%208.00249%2018.1001%207.29499%2018.1001H6.03249V18.6063H14.115V18.1001H12.8525C12.686%2018.1019%2012.5209%2018.0705%2012.3667%2018.0076C12.2125%2017.9448%2012.0725%2017.8517%2011.9548%2017.734C11.8371%2017.6163%2011.744%2017.4763%2011.6812%2017.3221C11.6183%2017.1679%2011.5869%2017.0028%2011.5887%2016.8363V3.95131H15.1262C16.54%203.95131%2017.6525%205.06381%2017.6525%206.47756H18.1562L17.6525%202.43506H2.49499V2.43631Z'%20fill='%23081735'/%3e%3c/svg%3e",import.meta.url).href,k=new URL(""+new URL("del-Cku5TBgi.png",import.meta.url).href,import.meta.url).href,K=new URL("data:image/png;base64,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",import.meta.url).href,e=p([]),_=()=>{if(e.value.length>0)for(let l=0;l<e.value.length;l++)e.value[l].type="over";e.value.unshift({name:"新建分组",is_show:!1,type:"edit",quickReply:[]}),S.value="新建分组",localStorage.setItem("selfQuick",JSON.stringify(e.value))},A=p(""),M=()=>{let l=JSON.parse(localStorage.getItem("selfQuick")),t=[];for(let a=0;a<l.length;a++){let C=[];for(let n=0;n<l[a].quickReply.length;n++)l[a].quickReply[n].name.indexOf(A.value)!==-1&&C.push(l[a].quickReply[n]);C.length>0&&(l[a].quickReply=C,t.push(l[a]))}A.value==""||!A.value?e.value=JSON.parse(localStorage.getItem("selfQuick")):e.value=t},S=p(),Q=(l,t)=>{t<0?e.value[l].name&&e.value[l].name!=""&&(e.value[l].type="over"):e.value[l].quickReply[t].name&&e.value[l].quickReply[t].name!=""&&(e.value[l].quickReply[t].type="over"),localStorage.setItem("selfQuick",JSON.stringify(e.value))},V=l=>{let t=!0;for(let a=0;a<e.value.length;a++){e.value[a].name?e.value[a].type="over":t=!1;for(let C=0;C<e.value[a].quickReply.length;C++)e.value[a].quickReply[C].name?e.value[a].quickReply[C].type="over":t=!1}t?(e.value[l].is_show=!0,e.value[l].quickReply.unshift({name:"新建分类",is_sel:!1,type:"edit",text:[]}),localStorage.setItem("selfQuick",JSON.stringify(e.value))):q({showClose:!0,message:"有为空的类目，请先完善再增加",type:"info"})},U=(l,t)=>{t==-1?e.value.splice(l,1):e.value[l].quickReply.splice(t,1),localStorage.setItem("selfQuick",JSON.stringify(e.value))},H=l=>{e.value[l].is_show=!e.value[l].is_show},u=p({index:-1,ind:-1}),N=(l,t)=>{u.value={index:l,ind:t},g.value=e.value[l].quickReply[t].text},X=(l,t)=>{e.value[l].quickReply[t].type="edit",S.value=e.value[l].quickReply[t].name,localStorage.setItem("selfQuick",JSON.stringify(e.value))};G(()=>{localStorage.getItem("selfQuick")&&(e.value=JSON.parse(localStorage.getItem("selfQuick")))}),p(!1);const g=p([]),b=()=>{u.value.index==-1||u.value.ind==-1?q({showClose:!0,message:"请先选择分类",type:"info"}):(e.value[u.value.index].quickReply[u.value.ind].text.unshift({message:""}),localStorage.setItem("selfQuick",JSON.stringify(e.value)))},J=()=>{let l=u.value.index,t=u.value.ind;e.value[l].quickReply[t].text=g.value,localStorage.setItem("selfQuick",JSON.stringify(e.value))},O=l=>{g.value.splice(l,1);let t=u.value.index,a=u.value.ind;e.value[t].quickReply[a].text=g.value,localStorage.setItem("selfQuick",JSON.stringify(e.value))},Z=p(!0);return W(()=>Y.query,(l,t)=>{l.chat=="slide"&&(Z.value=l.type=="true")},{immediate:!0}),(l,t)=>{const a=T("el-input"),C=T("el-form");return c(),d("div",t2,[s("div",{class:E(m(f).showHideWord?"main_cont quick_main_cont":"main_cont quick_main_cont small_tab")},[s("div",s2,[s("div",a2,[t[4]||(t[4]=s("div",{class:"tit"},[s("h2",null,"个人快捷回复"),s("span",null,"Personal quick reply")],-1)),s("div",o2,[s("div",n2,[t[3]||(t[3]=s("p",null,"目录",-1)),s("div",{onClick:_},[s("img",{src:m(I),alt:""},null,8,i2),t[2]||(t[2]=s("span",null,"添加分组",-1))])]),s("div",c2,[D(s("input",{type:"text",onInput:M,"onUpdate:modelValue":t[0]||(t[0]=n=>A.value=n),placeholder:"输入关键字进行过滤"},null,544),[[P,A.value]])])]),s("div",u2,[(c(!0),d(y,null,w(e.value,(n,o)=>(c(),d("div",C2,[s("div",{class:E(n.is_show?"name name_click":"name")},[n.type=="over"?(c(),d("div",d2,[s("span",{onClick:i=>H(o)},L(n.name),9,r2),s("div",{class:"addSon",onClick:i=>V(o)},[s("img",{src:m(I),alt:""},null,8,m2)],8,g2),s("div",{class:"addSon",onClick:i=>U(o,"-1")},[s("img",{src:m(k),alt:""},null,8,p2)],8,v2)])):h("",!0),n.type=="edit"?(c(),R(a,{key:1,onBlur:i=>Q(o,-1),modelValue:e.value[o].name,"onUpdate:modelValue":i=>e.value[o].name=i,placeholder:"请输入快"},null,8,["onBlur","modelValue","onUpdate:modelValue"])):h("",!0)],2),n.is_show?(c(!0),d(y,{key:0},w(n.quickReply,(i,v)=>(c(),d("div",h2,[s("div",{class:E(u.value.index==o&&u.value.ind==v?"name sel_names":"name")},[i.type=="over"?(c(),d("div",{key:0,class:"name_son",onClick:B=>N(o,v)},[s("span",null,L(i.name),1),i.type=="over"?(c(),d("div",{key:0,class:"addSon",onClick:B=>X(o,v)},[s("img",{src:m(K),alt:""},null,8,B2)],8,A2)):h("",!0),s("div",{class:"addSon",onClick:B=>U(o,v)},[s("img",{src:m(k),alt:""},null,8,y2)],8,k2)],8,f2)):h("",!0),i.type=="edit"?(c(),R(a,{key:1,onBlur:B=>Q(o,v),modelValue:e.value[o].quickReply[v].name,"onUpdate:modelValue":B=>e.value[o].quickReply[v].name=B,placeholder:"请输入"},null,8,["onBlur","modelValue","onUpdate:modelValue"])):h("",!0)],2)]))),256)):h("",!0)]))),256))])]),s("div",w2,[s("div",E2,[s("div",{class:"item",onClick:b},[s("img",{src:m(x),alt:""},null,8,I2),t[5]||(t[5]=s("p",null,"添加文字",-1))])]),s("div",S2,[F(C,{ref:"form",model:g.value,onSubmit:t[1]||(t[1]=$(()=>{},["prevent"]))},{default:z(()=>[(c(!0),d(y,null,w(g.value,(n,o)=>(c(),d("div",Q2,[s("div",U2,[F(a,{onBlur:J,modelValue:g.value[o].message,"onUpdate:modelValue":i=>g.value[o].message=i,placeholder:"请输入快捷回复内容"},null,8,["modelValue","onUpdate:modelValue"])]),s("div",{class:"del",onClick:i=>O(o)},[s("img",{src:m(k),alt:""},null,8,T2)],8,F2)]))),256))]),_:1},8,["model"])])])])],2)])}}};export{R2 as default};
//# sourceMappingURL=quick-C7q5Wpfp.js.map
