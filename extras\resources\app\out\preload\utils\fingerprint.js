"use strict";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};t.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var t=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new t.Error().stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="3f14ad09-1cdd-40d7-9469-f29e173a346a",t._sentryDebugIdIdentifier="sentry-dbid-3f14ad09-1cdd-40d7-9469-f29e173a346a")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});class a{constructor(e={}){this.options={enableNavigatorSpoof:!0,enableCanvasSpoof:!0,enableWebGLSpoof:!1,enableScreenSpoof:!1,enablePluginsSpoof:!1,...e}}init(){this.options.enableNavigatorSpoof&&this.spoofNavigator(),this.options.enableCanvasSpoof&&this.spoofCanvas(),this.options.enableWebGLSpoof&&this.spoofWebGL(),this.options.enableScreenSpoof&&this.spoofScreen(),this.options.enablePluginsSpoof&&this.spoofPlugins(),console.log("[FingerprintSpoofer] Initialized with options:",this.options)}spoofNavigator(){const e=(i,o,n)=>{Object.defineProperty(i,o,{get:()=>n,configurable:!0})};e(navigator,"userAgentData",{brands:[{brand:"Chromium",version:"137"},{brand:"Not/A)Brand",version:"8"}],mobile:!1,platform:"Windows",getHighEntropyValues:i=>Promise.resolve({platform:"Windows",platformVersion:"10.0.0",architecture:"x86",model:"",uaFullVersion:"137.0.0.0"})}),e(navigator,"language","en-US"),e(navigator,"languages",["en-US","en"]),e(navigator,"hardwareConcurrency",4),e(navigator,"deviceMemory",8)}spoofCanvas(){const e=HTMLCanvasElement.prototype.toDataURL;HTMLCanvasElement.prototype.toDataURL=function(...o){try{const n=this.getContext("2d");this._injectCanvasNoise(n,this.width,this.height)}catch{}return e.apply(this,o)};const i=CanvasRenderingContext2D.prototype.getImageData;CanvasRenderingContext2D.prototype.getImageData=function(...o){return this._injectCanvasNoise(this,this.canvas.width,this.canvas.height),i.apply(this,o)},CanvasRenderingContext2D.prototype._injectCanvasNoise=function(o,n,r){!o||typeof o.fillText!="function"||(o.save(),o.globalAlpha=.01,o.fillStyle="#000",o.font="16px Arial",o.fillText(`canvas-noise-${Math.random()*10}`,1,r-1),o.restore())}}spoofWebGL(){const e=WebGLRenderingContext.prototype.getParameter;WebGLRenderingContext.prototype.getParameter=function(o){const n={37445:"Fake GPU Corp.",37446:"Fake GPU Model 9000",33901:4096,3379:16};return o in n?n[o]:e.call(this,o)};const i=WebGLRenderingContext.prototype.getExtension;WebGLRenderingContext.prototype.getExtension=function(o){return o==="WEBGL_debug_renderer_info"?null:i.call(this,o)}}spoofScreen(){const e=(i,o,n)=>{Object.defineProperty(i,o,{get:()=>n,configurable:!0})};e(window.screen,"width",1920),e(window.screen,"height",1080),e(window.screen,"availWidth",1920),e(window.screen,"availHeight",1040),e(window.screen,"colorDepth",24),e(window.screen,"pixelDepth",24)}spoofPlugins(){const e={name:"Chrome PDF Plugin",filename:"internal-pdf-viewer",description:"Portable Document Format"};Object.defineProperty(navigator,"plugins",{get:()=>[e],configurable:!0}),Object.defineProperty(navigator,"mimeTypes",{get:()=>[{type:"application/pdf",suffixes:"pdf",description:"",enabledPlugin:e}],configurable:!0})}}const s=()=>new a({enableNavigatorSpoof:!0,enableCanvasSpoof:!0,enableWebGLSpoof:!1,enableScreenSpoof:!1,enablePluginsSpoof:!1});exports.FingerprintSpoofer=a;exports.createDefaultSpoofer=s;
//# sourceMappingURL=fingerprint.js.map
