"use strict";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};e.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},o=new e.Error().stack;o&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[o]="f91d7d19-ceaf-4ea5-a120-2b2a6c4b1a1c",e._sentryDebugIdIdentifier="sentry-dbid-f91d7d19-ceaf-4ea5-a120-2b2a6c4b1a1c")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const n=["Network Error","NetworkError","Failed to fetch","fetch is not defined","XMLHttpRequest","ERR_NETWORK","ERR_INTERNET_DISCONNECTED","ERR_CONNECTION_REFUSED","ERR_CONNECTION_RESET","ERR_CONNECTION_TIMED_OUT","ERR_NAME_NOT_RESOLVED","ENOTFOUND","ECONNREFUSED","ECONNRESET","ETIMEDOUT","ENETUNREACH","EHOSTUNREACH","timeout","Request timeout","net::ERR_","DNS_PROBE_FINISHED_NXDOMAIN","DNS_PROBE_FINISHED_NO_INTERNET"],t=["ResizeObserver loop limit exceeded","ResizeObserver loop completed with undelivered notifications","Non-Error promise rejection captured","Script error","Object Not Found Matching Id","IntersectionObserver","MutationObserver","PerformanceObserver"],r=["AbortError","The user aborted a request","Request was cancelled","cancelled","canceled","User cancelled","Operation was aborted","The operation was aborted"],i=["Permission denied","Permissions policy violation","NotAllowedError","SecurityError","Access denied","Blocked by CORS policy","Cross-Origin Request Blocked"],E=["facebook.com","google-analytics.com","googletagmanager.com","googlesyndication.com","doubleclick.net","extension://","chrome-extension://","moz-extension://","safari-extension://"],R=["HMR","Hot Module Replacement","webpack-dev-server","webpack-internal","[HMR]","hot-update"],d=["Loading chunk","Loading CSS chunk","ChunkLoadError","Failed to import","Module not found","Cannot resolve module","Dynamic import"],c=["Non-Error exception captured","Uncaught (in promise)","Promise rejected with no error","undefined is not an object","null is not an object","Cannot read property of undefined","Cannot read property of null"],s=[...n,...t,...r,...i,...E,...R,...d,...c],a=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop/,/^Loading chunk \d+ failed/,/^Loading CSS chunk \d+ failed/,/^ChunkLoadError/,/network|Network|NETWORK/i,/timeout|Timeout|TIMEOUT/i,/fetch|Fetch|FETCH.*failed/i,/connection|Connection|CONNECTION.*(refused|reset|timed|failed)/i,/ENOTFOUND|ECONNREFUSED|ECONNRESET|ETIMEDOUT|ENETUNREACH|EHOSTUNREACH/,/ERR_NETWORK|ERR_INTERNET|ERR_CONNECTION|ERR_NAME_NOT_RESOLVED/,/net::ERR_/,/DNS_PROBE_FINISHED/,/cancelled|canceled|abort/i,/permission.*denied/i,/not.*allowed/i,/access.*denied/i,/blocked.*by.*cors/i,/extension:\/\//,/chrome-extension/,/moz-extension/,/safari-extension/,/webpack|HMR|hot.*module/i,/undefined.*is.*not.*an.*object/i,/null.*is.*not.*an.*object/i,/cannot.*read.*property.*of.*(undefined|null)/i];function O(e){return e?["facebook.com","google-analytics.com","googletagmanager.com","googlesyndication.com","doubleclick.net","extension://","chrome-extension://","moz-extension://","safari-extension://","webpack-internal"].some(l=>e.includes(l)):!1}function u(e){if(!e)return!1;for(const o of s)if(e.includes(o))return!0;for(const o of a)if(o.test(e))return!0;return!1}function N(e){return e?["NetworkError","AbortError","SecurityError","NotAllowedError","ChunkLoadError"].includes(e):!1}exports.ALL_FILTERED_ERRORS=s;exports.BROWSER_COMPATIBILITY_ERRORS=t;exports.DEVELOPMENT_ERRORS=R;exports.ERROR_FILTER_PATTERNS=a;exports.NETWORK_ERRORS=n;exports.OTHER_FILTERED_ERRORS=c;exports.PERMISSION_ERRORS=i;exports.RESOURCE_LOADING_ERRORS=d;exports.THIRD_PARTY_ERRORS=E;exports.USER_ACTION_ERRORS=r;exports.shouldFilterByFilename=O;exports.shouldFilterByMessage=u;exports.shouldFilterByType=N;
//# sourceMappingURL=sentry-filters.js.map
