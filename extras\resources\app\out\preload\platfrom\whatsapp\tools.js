"use strict";(function(){try{var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};e.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var e=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},t=new e.Error().stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="30188c87-3c8e-4792-92b8-46ac3b351476",e._sentryDebugIdIdentifier="sentry-dbid-30188c87-3c8e-4792-92b8-46ac3b351476")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});class d{setTextContent(t="",n=document.querySelector("#main .lexical-rich-text-input > div")){if(n){const o=n.__lexicalEditor,i=o.parseEditorState({root:{children:[{children:[{detail:0,format:0,mode:"normal",style:"",text:t,type:"text",version:1}],direction:"ltr",format:"",indent:0,type:"paragraph",version:1}],direction:"ltr",format:"",indent:0,type:"root",version:1}});o.setEditorState(i)}}}exports.WhatsappTools=d;
//# sourceMappingURL=tools.js.map
