import{_ as Xo,r as qe,o as Ko,q as Qo,ab as Dn,ah as Hn,f as Vt,h as rt,F as Zo,B as Jo,n as we,y as Ft,A as Go,al as ts,ap as In,aq as es,s as ns,p as Nt}from"./index-BO8ZgokY.js";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};t.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var t=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new t.Error().stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="dace22c1-7ba5-4869-a14d-58626c03f74d",t._sentryDebugIdIdentifier="sentry-dbid-dace22c1-7ba5-4869-a14d-58626c03f74d")})()}catch{}/*!
 * OverlayScrollbars
 * Version: 2.11.5
 *
 * Copyright (c) Rene Haas | KingSora.
 * https://github.com/KingSora
 *
 * Released under the MIT license.
 */const yt=(t,e)=>{const{o:n,i:s,u:o}=t;let c=n,r;const u=(l,d)=>{const g=c,S=l,i=d||(s?!s(g,S):g!==S);return(i||o)&&(c=S,r=g),[c,i,r]};return[e?l=>u(e(c,r),l):u,l=>[c,!!l,r]]},os=typeof window<"u"&&typeof HTMLElement<"u"&&!!window.document,vt=os?window:{},to=Math.max,ss=Math.min,Ge=Math.round,$e=Math.abs,Ln=Math.sign,eo=vt.cancelAnimationFrame,pn=vt.requestAnimationFrame,vn=vt.setTimeout,no=vt.clearTimeout,Ie=t=>typeof vt[t]<"u"?vt[t]:void 0,cs=Ie("MutationObserver"),Pn=Ie("IntersectionObserver"),Ut=Ie("ResizeObserver"),ee=Ie("ScrollTimeline"),yn=t=>t===void 0,Le=t=>t===null,Mt=t=>typeof t=="number",re=t=>typeof t=="string",Pe=t=>typeof t=="boolean",Ct=t=>typeof t=="function",St=t=>Array.isArray(t),Ee=t=>typeof t=="object"&&!St(t)&&!Le(t),hn=t=>{const e=!!t&&t.length,n=Mt(e)&&e>-1&&e%1==0;return St(t)||!Ct(t)&&n?e>0&&Ee(t)?e-1 in t:!0:!1},Te=t=>!!t&&t.constructor===Object,Ae=t=>t instanceof HTMLElement,ze=t=>t instanceof Element;function Q(t,e){if(hn(t))for(let n=0;n<t.length&&e(t[n],n,t)!==!1;n++);else t&&Q(Object.keys(t),n=>e(t[n],n,t));return t}const oo=(t,e)=>t.indexOf(e)>=0,ne=(t,e)=>t.concat(e),nt=(t,e,n)=>(!re(e)&&hn(e)?Array.prototype.push.apply(t,e):t.push(e),t),Lt=t=>Array.from(t||[]),gn=t=>St(t)?t:!re(t)&&hn(t)?Lt(t):[t],ke=t=>!!t&&!t.length,tn=t=>Lt(new Set(t)),gt=(t,e,n)=>{Q(t,o=>o?o.apply(void 0,e||[]):!0),n||(t.length=0)},so="paddingTop",co="paddingRight",ro="paddingLeft",io="paddingBottom",lo="marginLeft",ao="marginRight",uo="marginBottom",bn="overflowX",mn="overflowY",Re="width",Ve="height",kt="visible",wt="hidden",Xt="scroll",rs=t=>{const e=String(t||"");return e?e[0].toUpperCase()+e.slice(1):""},Fe=(t,e,n,s)=>{if(t&&e){let o=!0;return Q(n,c=>{const r=t[c],u=e[c];r!==u&&(o=!1)}),o}return!1},fo=(t,e)=>Fe(t,e,["w","h"]),Ce=(t,e)=>Fe(t,e,["x","y"]),is=(t,e)=>Fe(t,e,["t","r","b","l"]),D=(t,...e)=>t.bind(0,...e),Bt=t=>{let e;const n=t?vn:pn,s=t?no:eo;return[o=>{s(e),e=n(()=>o(),Ct(t)?t():t)},()=>s(e)]},zn=t=>{const e=Ct(t)?t():t;if(Mt(e)){const n=e?vn:pn,s=e?no:eo;return o=>{const c=n(()=>o(),e);return()=>{s(c)}}}return e&&e._},Me=(t,e)=>{const{p:n,v:s,S:o,m:c}=e||{};let r,u,a,f,l;const d=function(A){u&&u(),r&&r(),l=u=r=a=void 0,t.apply(this,A)},g=C=>c&&a?c(a,C):C,S=()=>{u&&d(g(f)||f)},i=function(){const A=Lt(arguments),M=zn(n);if(M){const P=zn(s),x=g(A)||A,T=d.bind(0,x);u&&u(),o&&!l?(T(),l=!0,u=M(()=>l=void 0)):(u=M(T),P&&!r&&(r=P(S))),a=f=x}else d(A)};return i.O=S,i},po=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),xt=t=>t?Object.keys(t):[],j=(t,e,n,s,o,c,r)=>{const u=[e,n,s,o,c,r];return(typeof t!="object"||Le(t))&&!Ct(t)&&(t={}),Q(u,a=>{Q(a,(f,l)=>{const d=a[l];if(t===d)return!0;const g=St(d);if(d&&Te(d)){const S=t[l];let i=S;g&&!St(S)?i=[]:!g&&!Te(S)&&(i={}),t[l]=j(i,d)}else t[l]=g?d.slice():d})}),t},vo=(t,e)=>Q(j({},t),(n,s,o)=>{n===void 0?delete o[s]:n&&Te(n)&&(o[s]=vo(n))}),wn=t=>!xt(t).length,oe=()=>{},yo=(t,e,n)=>to(t,ss(e,n)),zt=t=>tn((St(t)?t:(t||"").split(" ")).filter(e=>e)),Sn=(t,e)=>t&&t.getAttribute(e),Rn=(t,e)=>t&&t.hasAttribute(e),Tt=(t,e,n)=>{Q(zt(e),s=>{t&&t.setAttribute(s,String(n||""))})},_t=(t,e)=>{Q(zt(e),n=>t&&t.removeAttribute(n))},Ne=(t,e)=>{const n=zt(Sn(t,e)),s=D(Tt,t,e),o=(c,r)=>{const u=new Set(n);return Q(zt(c),a=>{u[r](a)}),Lt(u).join(" ")};return{C:c=>s(o(c,"delete")),$:c=>s(o(c,"add")),H:c=>{const r=zt(c);return r.reduce((u,a)=>u&&n.includes(a),r.length>0)}}},ho=(t,e,n)=>(Ne(t,e).C(n),D(xn,t,e,n)),xn=(t,e,n)=>(Ne(t,e).$(n),D(ho,t,e,n)),De=(t,e,n,s)=>(s?xn:ho)(t,e,n),Cn=(t,e,n)=>Ne(t,e).H(n),go=t=>Ne(t,"class"),bo=(t,e)=>{go(t).C(e)},_n=(t,e)=>(go(t).$(e),D(bo,t,e)),mo=(t,e)=>{const n=e?ze(e)&&e:document;return n?Lt(n.querySelectorAll(t)):[]},ls=(t,e)=>{const n=e?ze(e)&&e:document;return n&&n.querySelector(t)},en=(t,e)=>ze(t)&&t.matches(e),wo=t=>en(t,"body"),nn=t=>t?Lt(t.childNodes):[],se=t=>t&&t.parentElement,jt=(t,e)=>ze(t)&&t.closest(e),on=t=>document.activeElement,as=(t,e,n)=>{const s=jt(t,e),o=t&&ls(n,s),c=jt(o,e)===s;return s&&o?s===t||o===t||c&&jt(jt(t,n),e)!==s:!1},Kt=t=>{Q(gn(t),e=>{const n=se(e);e&&n&&n.removeChild(e)})},pt=(t,e)=>D(Kt,t&&e&&Q(gn(e),n=>{n&&t.appendChild(n)}));let So;const us=()=>So,ds=t=>{So=t},Yt=t=>{const e=document.createElement("div");return Tt(e,"class",t),e},xo=t=>{const e=Yt(),n=us(),s=t.trim();return e.innerHTML=n?n.createHTML(s):s,Q(nn(e),o=>Kt(o))},Vn=(t,e)=>t.getPropertyValue(e)||t[e]||"",Co=t=>{const e=t||0;return isFinite(e)?e:0},Se=t=>Co(parseFloat(t||"")),sn=t=>Math.round(t*1e4)/1e4,_o=t=>`${sn(Co(t))}px`;function ce(t,e){t&&e&&Q(e,(n,s)=>{try{const o=t.style,c=Le(n)||Pe(n)?"":Mt(n)?_o(n):n;s.indexOf("--")===0?o.setProperty(s,c):o[s]=c}catch{}})}function $t(t,e,n){const s=re(e);let o=s?"":{};if(t){const c=vt.getComputedStyle(t,n)||t.style;o=s?Vn(c,e):Lt(e).reduce((r,u)=>(r[u]=Vn(c,u),r),o)}return o}const Fn=(t,e,n)=>{const s=e?`${e}-`:"",o=n?`-${n}`:"",c=`${s}top${o}`,r=`${s}right${o}`,u=`${s}bottom${o}`,a=`${s}left${o}`,f=$t(t,[c,r,u,a]);return{t:Se(f[c]),r:Se(f[r]),b:Se(f[u]),l:Se(f[a])}},Xe=(t,e)=>`translate${Ee(t)?`(${t.x},${t.y})`:`${e?"X":"Y"}(${t})`}`,fs=t=>!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length),ps={w:0,h:0},Ue=(t,e)=>e?{w:e[`${t}Width`],h:e[`${t}Height`]}:ps,vs=t=>Ue("inner",t||vt),qt=D(Ue,"offset"),Oo=D(Ue,"client"),He=D(Ue,"scroll"),On=t=>{const e=parseFloat($t(t,Re))||0,n=parseFloat($t(t,Ve))||0;return{w:e-Ge(e),h:n-Ge(n)}},Ke=t=>t.getBoundingClientRect(),ys=t=>!!t&&fs(t),cn=t=>!!(t&&(t[Ve]||t[Re])),$o=(t,e)=>{const n=cn(t);return!cn(e)&&n},Nn=(t,e,n,s)=>{Q(zt(e),o=>{t&&t.removeEventListener(o,n,s)})},J=(t,e,n,s)=>{var o;const c=(o=s&&s.T)!=null?o:!0,r=s&&s.I||!1,u=s&&s.A||!1,a={passive:c,capture:r};return D(gt,zt(e).map(f=>{const l=u?d=>{Nn(t,f,l,r),n&&n(d)}:n;return t&&t.addEventListener(f,l,a),D(Nn,t,f,l,r)}))},Eo=t=>t.stopPropagation(),rn=t=>t.preventDefault(),To=t=>Eo(t)||rn(t),Ot=(t,e)=>{const{x:n,y:s}=Mt(e)?{x:e,y:e}:e||{};Mt(n)&&(t.scrollLeft=n),Mt(s)&&(t.scrollTop=s)},ht=t=>({x:t.scrollLeft,y:t.scrollTop}),Ao=()=>({D:{x:0,y:0},M:{x:0,y:0}}),hs=(t,e)=>{const{D:n,M:s}=t,{w:o,h:c}=e,r=(d,g,S)=>{let i=Ln(d)*S,C=Ln(g)*S;if(i===C){const A=$e(d),M=$e(g);C=A>M?0:C,i=A<M?0:i}return i=i===C?0:i,[i+0,C+0]},[u,a]=r(n.x,s.x,o),[f,l]=r(n.y,s.y,c);return{D:{x:u,y:f},M:{x:a,y:l}}},Qe=({D:t,M:e})=>{const n=(s,o)=>s===0&&s<=o;return{x:n(t.x,e.x),y:n(t.y,e.y)}},Un=({D:t,M:e},n)=>{const s=(o,c,r)=>yo(0,1,(o-r)/(o-c)||0);return{x:s(t.x,e.x,n.x),y:s(t.y,e.y,n.y)}},ln=t=>{t&&t.focus&&t.focus({preventScroll:!0})},Bn=(t,e)=>{Q(gn(e),t)},an=t=>{const e=new Map,n=(c,r)=>{if(c){const u=e.get(c);Bn(a=>{u&&u[a?"delete":"clear"](a)},r)}else e.forEach(u=>{u.clear()}),e.clear()},s=(c,r)=>{if(re(c)){const f=e.get(c)||new Set;return e.set(c,f),Bn(l=>{Ct(l)&&f.add(l)},r),D(n,c,r)}Pe(r)&&r&&n();const u=xt(c),a=[];return Q(u,f=>{const l=c[f];l&&nt(a,s(f,l))}),D(gt,a)},o=(c,r)=>{Q(Lt(e.get(c)),u=>{r&&!ke(r)?u.apply(0,r):u()})};return s(t||{}),[s,n,o]},ko={},Mo={},gs=t=>{Q(t,e=>Q(e,(n,s)=>{ko[s]=e[s]}))},Do=(t,e,n)=>xt(t).map(s=>{const{static:o,instance:c}=t[s],[r,u,a]=n||[],f=n?c:o;if(f){const l=n?f(r,u,e):f(e);return(a||Mo)[s]=l}}),ie=t=>Mo[t],bs="__osOptionsValidationPlugin",Qt="data-overlayscrollbars",_e="os-environment",xe=`${_e}-scrollbar-hidden`,Ze=`${Qt}-initialize`,Oe="noClipping",jn=`${Qt}-body`,Ht=Qt,ms="host",At=`${Qt}-viewport`,ws=bn,Ss=mn,xs="arrange",Ho="measuring",Cs="scrolling",Io="scrollbarHidden",_s="noContent",un=`${Qt}-padding`,Wn=`${Qt}-content`,$n="os-size-observer",Os=`${$n}-appear`,$s=`${$n}-listener`,Es="os-trinsic-observer",Ts="os-theme-none",bt="os-scrollbar",As=`${bt}-rtl`,ks=`${bt}-horizontal`,Ms=`${bt}-vertical`,Lo=`${bt}-track`,En=`${bt}-handle`,Ds=`${bt}-visible`,Hs=`${bt}-cornerless`,Yn=`${bt}-interaction`,qn=`${bt}-unusable`,dn=`${bt}-auto-hide`,Xn=`${dn}-hidden`,Kn=`${bt}-wheel`,Is=`${Lo}-interactive`,Ls=`${En}-interactive`,Ps="__osSizeObserverPlugin",zs=(t,e)=>{const{k:n}=e,[s,o]=t("showNativeOverlaidScrollbars");return[s&&n.x&&n.y,o]},Wt=t=>t.indexOf(kt)===0,Rs=t=>t.replace(`${kt}-`,""),fn=(t,e)=>{if(t==="auto")return e?Xt:wt;const n=t||wt;return[wt,Xt,kt].includes(n)?n:wt},Vs=(t,e)=>{const{overflowX:n,overflowY:s}=$t(t,[bn,mn]);return{x:fn(n,e.x),y:fn(s,e.y)}},Po="__osScrollbarsHidingPlugin",Fs="__osClickScrollPlugin",Qn=t=>JSON.stringify(t,(e,n)=>{if(Ct(n))throw 0;return n}),Zn=(t,e)=>t?`${e}`.split(".").reduce((n,s)=>n&&po(n,s)?n[s]:void 0,t):void 0,Ns={paddingAbsolute:!1,showNativeOverlaidScrollbars:!1,update:{elementEvents:[["img","load"]],debounce:[0,33],attributes:null,ignoreMutation:null},overflow:{x:"scroll",y:"scroll"},scrollbars:{theme:"os-theme-dark",visibility:"auto",autoHide:"never",autoHideDelay:1300,autoHideSuspend:!1,dragScroll:!0,clickScroll:!1,pointers:["mouse","touch","pen"]}},zo=(t,e)=>{const n={},s=ne(xt(e),xt(t));return Q(s,o=>{const c=t[o],r=e[o];if(Ee(c)&&Ee(r))j(n[o]={},zo(c,r)),wn(n[o])&&delete n[o];else if(po(e,o)&&r!==c){let u=!0;if(St(c)||St(r))try{Qn(c)===Qn(r)&&(u=!1)}catch{}u&&(n[o]=r)}}),n},Jn=(t,e,n)=>s=>[Zn(t,s),n||Zn(e,s)!==void 0];let Ro;const Us=()=>Ro,Bs=t=>{Ro=t};let Je;const js=()=>{const t=(x,T,H)=>{pt(document.body,x),pt(document.body,x);const I=Oo(x),V=qt(x),k=On(T);return H&&Kt(x),{x:V.h-I.h+k.h,y:V.w-I.w+k.w}},e=x=>{let T=!1;const H=_n(x,xe);try{T=$t(x,"scrollbar-width")==="none"||$t(x,"display","::-webkit-scrollbar")==="none"}catch{}return H(),T},n=`.${_e}{scroll-behavior:auto!important;position:fixed;opacity:0;visibility:hidden;overflow:scroll;height:200px;width:200px;z-index:-1}.${_e} div{width:200%;height:200%;margin:10px 0}.${xe}{scrollbar-width:none!important}.${xe}::-webkit-scrollbar,.${xe}::-webkit-scrollbar-corner{appearance:none!important;display:none!important;width:0!important;height:0!important}`,o=xo(`<div class="${_e}"><div></div><style>${n}</style></div>`)[0],c=o.firstChild,r=o.lastChild,u=Us();u&&(r.nonce=u);const[a,,f]=an(),[l,d]=yt({o:t(o,c),i:Ce},D(t,o,c,!0)),[g]=d(),S=e(o),i={x:g.x===0,y:g.y===0},C={elements:{host:null,padding:!S,viewport:x=>S&&wo(x)&&x,content:!1},scrollbars:{slot:!0},cancel:{nativeScrollbarsOverlaid:!1,body:null}},A=j({},Ns),M=D(j,{},A),P=D(j,{},C),q={P:g,k:i,U:S,J:!!ee,G:D(a,"r"),K:P,Z:x=>j(C,x)&&P(),tt:M,nt:x=>j(A,x)&&M(),ot:j({},C),st:j({},A)};if(_t(o,"style"),Kt(o),J(vt,"resize",()=>{f("r",[])}),Ct(vt.matchMedia)&&!S&&(!i.x||!i.y)){const x=T=>{const H=vt.matchMedia(`(resolution: ${vt.devicePixelRatio}dppx)`);J(H,"change",()=>{T(),x(T)},{A:!0})};x(()=>{const[T,H]=l();j(q.P,T),f("r",[H])})}return q},Et=()=>(Je||(Je=js()),Je),Ws=(t,e,n)=>{let s=!1;const o=n?new WeakMap:!1,c=()=>{s=!0},r=u=>{if(o&&n){const a=n.map(f=>{const[l,d]=f||[];return[d&&l?(u||mo)(l,t):[],d]});Q(a,f=>Q(f[0],l=>{const d=f[1],g=o.get(l)||[];if(t.contains(l)&&d){const i=J(l,d,C=>{s?(i(),o.delete(l)):e(C)});o.set(l,nt(g,i))}else gt(g),o.delete(l)}))}};return r(),[c,r]},Gn=(t,e,n,s)=>{let o=!1;const{et:c,ct:r,rt:u,it:a,lt:f,ut:l}=s||{},d=Me(()=>o&&n(!0),{p:33,v:99}),[g,S]=Ws(t,d,u),i=c||[],C=r||[],A=ne(i,C),M=(q,x)=>{if(!ke(x)){const T=f||oe,H=l||oe,I=[],V=[];let k=!1,m=!1;if(Q(x,O=>{const{attributeName:_,target:z,type:F,oldValue:W,addedNodes:Y,removedNodes:B}=O,K=F==="attributes",U=F==="childList",ot=t===z,E=K&&_,h=E&&Sn(z,_||""),p=re(h)?h:null,b=E&&W!==p,v=oo(C,_)&&b;if(e&&(U||!ot)){const y=K&&b,w=y&&a&&en(z,a),L=(w?!T(z,_,W,p):!K||y)&&!H(O,!!w,t,s);Q(Y,N=>nt(I,N)),Q(B,N=>nt(I,N)),m=m||L}!e&&ot&&b&&!T(z,_,W,p)&&(nt(V,_),k=k||v)}),S(O=>tn(I).reduce((_,z)=>(nt(_,mo(O,z)),en(z,O)?nt(_,z):_),[])),e)return!q&&m&&n(!1),[!1];if(!ke(V)||k){const O=[tn(V),k];return q||n.apply(0,O),O}}},P=new cs(D(M,!1));return[()=>(P.observe(t,{attributes:!0,attributeOldValue:!0,attributeFilter:A,subtree:e,childList:e,characterData:e}),o=!0,()=>{o&&(g(),P.disconnect(),o=!1)}),()=>{if(o)return d.O(),M(!0,P.takeRecords())}]};let Pt=null;const Vo=(t,e,n)=>{const{ft:s}=n||{},o=ie(Ps),[c]=yt({o:!1,u:!0});return()=>{const r=[],a=xo(`<div class="${$n}"><div class="${$s}"></div></div>`)[0],f=a.firstChild,l=d=>{const g=St(d)&&!ke(d);let S=!1,i=!1;if(g){const C=d[0],[A,,M]=c(C.contentRect),P=cn(A);i=$o(A,M),S=!i&&!P}else i=d===!0;S||e({_t:!0,ft:i})};if(Ut){if(!Pe(Pt)){const i=new Ut(oe);i.observe(t,{get box(){Pt=!0}}),Pt=Pt||!1,i.disconnect()}const d=Me(l,{p:0,v:0}),g=i=>d(i),S=new Ut(g);if(S.observe(Pt?t:f),nt(r,[()=>{S.disconnect()},!Pt&&pt(t,a)]),Pt){const i=new Ut(g);i.observe(t,{box:"border-box"}),nt(r,()=>i.disconnect())}}else if(o){const[d,g]=o(f,l,s);nt(r,ne([_n(a,Os),J(a,"animationstart",d),pt(t,a)],g))}else return oe;return D(gt,r)}},Ys=(t,e)=>{let n;const s=a=>a.h===0||a.isIntersecting||a.intersectionRatio>0,o=Yt(Es),[c]=yt({o:!1}),r=(a,f)=>{if(a){const l=c(s(a)),[,d]=l;return d&&!f&&e(l)&&[l]}},u=(a,f)=>r(f.pop(),a);return[()=>{const a=[];if(Pn)n=new Pn(D(u,!1),{root:t}),n.observe(o),nt(a,()=>{n.disconnect()});else{const f=()=>{const l=qt(o);r(l)};nt(a,Vo(o,f)()),f()}return D(gt,nt(a,pt(t,o)))},()=>n&&u(!0,n.takeRecords())]},qs=(t,e,n,s)=>{let o,c,r,u,a,f;const l=`[${Ht}]`,d=`[${At}]`,g=["id","class","style","open","wrap","cols","rows"],{dt:S,vt:i,L:C,gt:A,ht:M,V:P,bt:q,wt:x,yt:T,St:H}=t,I=v=>$t(v,"direction")==="rtl",V={Ot:!1,B:I(S)},k=Et(),m=ie(Po),[O]=yt({i:fo,o:{w:0,h:0}},()=>{const v=m&&m.R(t,e,V,k,n).Y,w=!(q&&P)&&Cn(i,Ht,Oe),$=!P&&x(xs),L=$&&ht(A),N=L&&H(),G=T(Ho,w),X=$&&v&&v(),tt=He(C),R=On(C);return X&&X(),Ot(A,L),N&&N(),w&&G(),{w:tt.w+R.w,h:tt.h+R.h}}),_=Me(s,{p:()=>o,v:()=>c,m(v,y){const[w]=v,[$]=y;return[ne(xt(w),xt($)).reduce((L,N)=>(L[N]=w[N]||$[N],L),{})]}}),z=v=>{const y=I(S);j(v,{Ct:f!==y}),j(V,{B:y}),f=y},F=(v,y)=>{const[w,$]=v,L={$t:$};return j(V,{Ot:w}),y||s(L),L},W=({_t:v,ft:y})=>{const $=!(v&&!y)&&k.U?_:s,L={_t:v||y,ft:y};z(L),$(L)},Y=(v,y)=>{const[,w]=O(),$={xt:w};return z($),w&&!y&&(v?s:_)($),$},B=(v,y,w)=>{const $={Ht:y};return z($),y&&!w&&_($),$},[K,U]=M?Ys(i,F):[],ot=!P&&Vo(i,W,{ft:!0}),[E,h]=Gn(i,!1,B,{ct:g,et:g}),p=P&&Ut&&new Ut(v=>{const y=v[v.length-1].contentRect;W({_t:!0,ft:$o(y,a)}),a=y}),b=Me(()=>{const[,v]=O();s({xt:v,_t:q})},{p:222,S:!0});return[()=>{p&&p.observe(i);const v=ot&&ot(),y=K&&K(),w=E(),$=k.G(L=>{L?_({Et:L}):b()});return()=>{p&&p.disconnect(),v&&v(),y&&y(),u&&u(),w(),$()}},({zt:v,Tt:y,It:w})=>{const $={},[L]=v("update.ignoreMutation"),[N,G]=v("update.attributes"),[X,tt]=v("update.elementEvents"),[R,it]=v("update.debounce"),dt=tt||G,lt=y||w,ut=et=>Ct(L)&&L(et);if(dt){r&&r(),u&&u();const[et,st]=Gn(M||C,!0,Y,{et:ne(g,N||[]),rt:X,it:l,ut:(ct,Z)=>{const{target:at,attributeName:ft}=ct;return(!Z&&ft&&!P?as(at,l,d):!1)||!!jt(at,`.${bt}`)||!!ut(ct)}});u=et(),r=st}if(it)if(_.O(),St(R)){const et=R[0],st=R[1];o=Mt(et)&&et,c=Mt(st)&&st}else Mt(R)?(o=R,c=!1):(o=!1,c=!1);if(lt){const et=h(),st=U&&U(),ct=r&&r();et&&j($,B(et[0],et[1],lt)),st&&j($,F(st[0],lt)),ct&&j($,Y(ct[0],lt))}return z($),$},V]},Fo=(t,e)=>Ct(e)?e.apply(0,t):e,Xs=(t,e,n,s)=>{const o=yn(s)?n:s;return Fo(t,o)||e.apply(0,t)},No=(t,e,n,s)=>{const o=yn(s)?n:s,c=Fo(t,o);return!!c&&(Ae(c)?c:e.apply(0,t))},Ks=(t,e)=>{const{nativeScrollbarsOverlaid:n,body:s}=e||{},{k:o,U:c,K:r}=Et(),{nativeScrollbarsOverlaid:u,body:a}=r().cancel,f=n??u,l=yn(s)?a:s,d=(o.x||o.y)&&f,g=t&&(Le(l)?!c:l);return!!d||!!g},Qs=(t,e,n,s)=>{const o="--os-viewport-percent",c="--os-scroll-percent",r="--os-scroll-direction",{K:u}=Et(),{scrollbars:a}=u(),{slot:f}=a,{dt:l,vt:d,L:g,At:S,gt:i,bt:C,V:A}=e,{scrollbars:M}=S?{}:t,{slot:P}=M||{},q=[],x=[],T=[],H=No([l,d,g],()=>A&&C?l:d,f,P),I=E=>{if(ee){let h=null,p=[];const b=new ee({source:i,axis:E}),v=()=>{h&&h.cancel(),h=null};return{kt:w=>{const{Dt:$}=n,L=Qe($)[E],N=E==="x",G=[Xe(0,N),Xe(`calc(100cq${N?"w":"h"} + -100%)`,N)],X=L?G:G.reverse();return p[0]===X[0]&&p[1]===X[1]||(v(),p=X,h=w.Mt.animate({clear:["left"],transform:X},{timeline:b})),v}}}},V={x:I("x"),y:I("y")},k=()=>{const{Rt:E,Vt:h}=n,p=(b,v)=>yo(0,1,b/(b+v)||0);return{x:p(h.x,E.x),y:p(h.y,E.y)}},m=(E,h,p)=>{const b=p?_n:bo;Q(E,v=>{b(v.Lt,h)})},O=(E,h)=>{Q(E,p=>{const[b,v]=h(p);ce(b,v)})},_=(E,h,p)=>{const b=Pe(p),v=b?p:!0,y=b?!p:!0;v&&m(x,E,h),y&&m(T,E,h)},z=()=>{const E=k(),h=p=>b=>[b.Lt,{[o]:sn(p)+""}];O(x,h(E.x)),O(T,h(E.y))},F=()=>{if(!ee){const{Dt:E}=n,h=Un(E,ht(i)),p=b=>v=>[v.Lt,{[c]:sn(b)+""}];O(x,p(h.x)),O(T,p(h.y))}},W=()=>{const{Dt:E}=n,h=Qe(E),p=b=>v=>[v.Lt,{[r]:b?"0":"1"}];O(x,p(h.x)),O(T,p(h.y)),ee&&(x.forEach(V.x.kt),T.forEach(V.y.kt))},Y=()=>{if(A&&!C){const{Rt:E,Dt:h}=n,p=Qe(h),b=Un(h,ht(i)),v=y=>{const{Lt:w}=y,$=se(w)===g&&w,L=(N,G,X)=>{const tt=G*N;return _o(X?tt:-tt)};return[$,$&&{transform:Xe({x:L(b.x,E.x,p.x),y:L(b.y,E.y,p.y)})}]};O(x,v),O(T,v)}},B=E=>{const h=E?"x":"y",b=Yt(`${bt} ${E?ks:Ms}`),v=Yt(Lo),y=Yt(En),w={Lt:b,Ut:v,Mt:y},$=V[h];return nt(E?x:T,w),nt(q,[pt(b,v),pt(v,y),D(Kt,b),$&&$.kt(w),s(w,_,E)]),w},K=D(B,!0),U=D(B,!1),ot=()=>(pt(H,x[0].Lt),pt(H,T[0].Lt),D(gt,q));return K(),U(),[{Pt:z,Nt:F,qt:W,Bt:Y,Ft:_,jt:{Xt:x,Yt:K,Wt:D(O,x)},Jt:{Xt:T,Yt:U,Wt:D(O,T)}},ot]},Zs=(t,e,n,s)=>(o,c,r)=>{const{vt:u,L:a,V:f,gt:l,Gt:d,St:g}=e,{Lt:S,Ut:i,Mt:C}=o,[A,M]=Bt(333),[P,q]=Bt(444),x=I=>{Ct(l.scrollBy)&&l.scrollBy({behavior:"smooth",left:I.x,top:I.y})},T=()=>{const I="pointerup pointercancel lostpointercapture",V=`client${r?"X":"Y"}`,k=r?Re:Ve,m=r?"left":"top",O=r?"w":"h",_=r?"x":"y",z=(W,Y)=>B=>{const{Rt:K}=n,U=qt(i)[O]-qt(C)[O],E=Y*B/U*K[_];Ot(l,{[_]:W+E})},F=[];return J(i,"pointerdown",W=>{const Y=jt(W.target,`.${En}`)===C,B=Y?C:i,K=t.scrollbars,U=K[Y?"dragScroll":"clickScroll"],{button:ot,isPrimary:E,pointerType:h}=W,{pointers:p}=K;if(ot===0&&E&&U&&(p||[]).includes(h)){gt(F),q();const v=!Y&&(W.shiftKey||U==="instant"),y=D(Ke,C),w=D(Ke,i),$=(Z,at)=>(Z||y())[m]-(at||w())[m],L=Ge(Ke(l)[k])/qt(l)[O]||1,N=z(ht(l)[_],1/L),G=W[V],X=y(),tt=w(),R=X[k],it=$(X,tt)+R/2,dt=G-tt[m],lt=Y?0:dt-it,ut=Z=>{gt(ct),B.releasePointerCapture(Z.pointerId)},et=Y||v,st=g(),ct=[J(d,I,ut),J(d,"selectstart",Z=>rn(Z),{T:!1}),J(i,I,ut),et&&J(i,"pointermove",Z=>N(lt+(Z[V]-G))),et&&(()=>{const Z=ht(l);st();const at=ht(l),ft={x:at.x-Z.x,y:at.y-Z.y};($e(ft.x)>3||$e(ft.y)>3)&&(g(),Ot(l,Z),x(ft),P(st))})];if(B.setPointerCapture(W.pointerId),v)N(lt);else if(!Y){const Z=ie(Fs);if(Z){const at=Z(N,lt,R,ft=>{ft?st():nt(ct,st)});nt(ct,at),nt(F,D(at,!0))}}}})};let H=!0;return D(gt,[J(C,"pointermove pointerleave",s),J(S,"pointerenter",()=>{c(Yn,!0)}),J(S,"pointerleave pointercancel",()=>{c(Yn,!1)}),!f&&J(S,"mousedown",()=>{const I=on();(Rn(I,At)||Rn(I,Ht)||I===document.body)&&vn(D(ln,a),25)}),J(S,"wheel",I=>{const{deltaX:V,deltaY:k,deltaMode:m}=I;H&&m===0&&se(S)===u&&x({x:V,y:k}),H=!1,c(Kn,!0),A(()=>{H=!0,c(Kn)}),rn(I)},{T:!1,I:!0}),J(S,"pointerdown",()=>{const I=J(d,"click",k=>{V(),To(k)},{A:!0,I:!0,T:!1}),V=J(d,"pointerup pointercancel",()=>{V(),setTimeout(I,150)},{I:!0,T:!0})},{I:!0,T:!0}),T(),M,q])},Js=(t,e,n,s,o,c)=>{let r,u,a,f,l,d=oe,g=0;const S=["mouse","pen"],i=h=>S.includes(h.pointerType),[C,A]=Bt(),[M,P]=Bt(100),[q,x]=Bt(100),[T,H]=Bt(()=>g),[I,V]=Qs(t,o,s,Zs(e,o,s,h=>i(h)&&K())),{vt:k,Kt:m,bt:O}=o,{Ft:_,Pt:z,Nt:F,qt:W,Bt:Y}=I,B=(h,p)=>{if(H(),h)_(Xn);else{const b=D(_,Xn,!0);g>0&&!p?T(b):b()}},K=()=>{(a?!r:!f)&&(B(!0),M(()=>{B(!1)}))},U=h=>{_(dn,h,!0),_(dn,h,!1)},ot=h=>{i(h)&&(r=a,a&&B(!0))},E=[H,P,x,A,()=>d(),J(k,"pointerover",ot,{A:!0}),J(k,"pointerenter",ot),J(k,"pointerleave",h=>{i(h)&&(r=!1,a&&B(!1))}),J(k,"pointermove",h=>{i(h)&&u&&K()}),J(m,"scroll",h=>{C(()=>{F(),K()}),c(h),Y()})];return[()=>D(gt,nt(E,V())),({zt:h,It:p,Qt:b,Zt:v})=>{const{tn:y,nn:w,sn:$,en:L}=v||{},{Ct:N,ft:G}=b||{},{B:X}=n,{k:tt}=Et(),{cn:R,j:it}=s,[dt,lt]=h("showNativeOverlaidScrollbars"),[ut,et]=h("scrollbars.theme"),[st,ct]=h("scrollbars.visibility"),[Z,at]=h("scrollbars.autoHide"),[ft,Zt]=h("scrollbars.autoHideSuspend"),[le]=h("scrollbars.autoHideDelay"),[ae,ue]=h("scrollbars.dragScroll"),[de,fe]=h("scrollbars.clickScroll"),[Dt,Jt]=h("overflow"),Be=G&&!p,je=it.x||it.y,We=y||w||L||N||p,Ye=$||ct||Jt,mt=dt&&tt.x&&tt.y,pe=(Rt,Gt,ve)=>{const ye=Rt.includes(Xt)&&(st===kt||st==="auto"&&Gt===Xt);return _(Ds,ye,ve),ye};if(g=le,Be&&(ft&&je?(U(!1),d(),q(()=>{d=J(m,"scroll",D(U,!0),{A:!0})})):U(!0)),lt&&_(Ts,mt),et&&(_(l),_(ut,!0),l=ut),Zt&&!ft&&U(!0),at&&(u=Z==="move",a=Z==="leave",f=Z==="never",B(f,!0)),ue&&_(Ls,ae),fe&&_(Is,!!de),Ye){const Rt=pe(Dt.x,R.x,!0),Gt=pe(Dt.y,R.y,!1);_(Hs,!(Rt&&Gt))}We&&(F(),z(),Y(),L&&W(),_(qn,!it.x,!0),_(qn,!it.y,!1),_(As,X&&!O))},{},I]},Gs=t=>{const e=Et(),{K:n,U:s}=e,{elements:o}=n(),{padding:c,viewport:r,content:u}=o,a=Ae(t),f=a?{}:t,{elements:l}=f,{padding:d,viewport:g,content:S}=l||{},i=a?t:f.target,C=wo(i),A=i.ownerDocument,M=A.documentElement,P=()=>A.defaultView||vt,q=D(Xs,[i]),x=D(No,[i]),T=D(Yt,""),H=D(q,T,r),I=D(x,T,u),V=R=>{const it=qt(R),dt=He(R),lt=$t(R,bn),ut=$t(R,mn);return dt.w-it.w>0&&!Wt(lt)||dt.h-it.h>0&&!Wt(ut)},k=H(g),m=k===i,O=m&&C,_=!m&&I(S),z=!m&&k===_,F=O?M:k,W=O?F:i,Y=!m&&x(T,c,d),B=!z&&_,K=[B,F,Y,W].map(R=>Ae(R)&&!se(R)&&R),U=R=>R&&oo(K,R),ot=!U(F)&&V(F)?F:i,E=O?M:F,p={dt:i,vt:W,L:F,rn:Y,ht:B,gt:E,Kt:O?A:F,ln:C?M:ot,Gt:A,bt:C,At:a,V:m,an:P,wt:R=>Cn(F,At,R),yt:(R,it)=>De(F,At,R,it),St:()=>De(E,At,Cs,!0)},{dt:b,vt:v,rn:y,L:w,ht:$}=p,L=[()=>{_t(v,[Ht,Ze]),_t(b,Ze),C&&_t(M,[Ze,Ht])}];let N=nn([$,w,y,v,b].find(R=>R&&!U(R)));const G=O?b:$||w,X=D(gt,L);return[p,()=>{const R=P(),it=on(),dt=ct=>{pt(se(ct),nn(ct)),Kt(ct)},lt=ct=>J(ct,"focusin focusout focus blur",To,{I:!0,T:!1}),ut="tabindex",et=Sn(w,ut),st=lt(it);return Tt(v,Ht,m?"":ms),Tt(y,un,""),Tt(w,At,""),Tt($,Wn,""),m||(Tt(w,ut,et||"-1"),C&&Tt(M,jn,"")),pt(G,N),pt(v,y),pt(y||v,!m&&w),pt(w,$),nt(L,[st,()=>{const ct=on(),Z=U(w),at=Z&&ct===w?b:ct,ft=lt(at);_t(y,un),_t($,Wn),_t(w,At),C&&_t(M,jn),et?Tt(w,ut,et):_t(w,ut),U($)&&dt($),Z&&dt(w),U(y)&&dt(y),ln(at),ft()}]),s&&!m&&(xn(w,At,Io),nt(L,D(_t,w,At))),ln(!m&&C&&it===b&&R.top===R?w:it),st(),N=0,X},X]},tc=({ht:t})=>({Qt:e,un:n,It:s})=>{const{$t:o}=e||{},{Ot:c}=n;t&&(o||s)&&ce(t,{[Ve]:c&&"100%"})},ec=({vt:t,rn:e,L:n,V:s},o)=>{const[c,r]=yt({i:is,o:Fn()},D(Fn,t,"padding",""));return({zt:u,Qt:a,un:f,It:l})=>{let[d,g]=r(l);const{U:S}=Et(),{_t:i,xt:C,Ct:A}=a||{},{B:M}=f,[P,q]=u("paddingAbsolute");(i||g||(l||C))&&([d,g]=c(l));const T=!s&&(q||A||g);if(T){const H=!P||!e&&!S,I=d.r+d.l,V=d.t+d.b,k={[ao]:H&&!M?-I:0,[uo]:H?-V:0,[lo]:H&&M?-I:0,top:H?-d.t:0,right:H?M?-d.r:"auto":0,left:H?M?"auto":-d.l:0,[Re]:H&&`calc(100% + ${I}px)`},m={[so]:H?d.t:0,[co]:H?d.r:0,[io]:H?d.b:0,[ro]:H?d.l:0};ce(e||n,k),ce(n,m),j(o,{rn:d,fn:!H,F:e?m:j({},k,m)})}return{_n:T}}},nc=(t,e)=>{const n=Et(),{vt:s,rn:o,L:c,V:r,Kt:u,gt:a,bt:f,yt:l,an:d}=t,{U:g}=n,S=f&&r,i=D(to,0),C={display:()=>!1,direction:p=>p!=="ltr",flexDirection:p=>p.endsWith("-reverse"),writingMode:p=>p!=="horizontal-tb"},A=xt(C),M={i:fo,o:{w:0,h:0}},P={i:Ce,o:{}},q=p=>{l(Ho,!S&&p)},x=p=>{if(!A.some(X=>{const tt=p[X];return tt&&C[X](tt)}))return{D:{x:0,y:0},M:{x:1,y:1}};q(!0);const v=ht(a),y=l(_s,!0),w=J(u,Xt,X=>{const tt=ht(a);X.isTrusted&&tt.x===v.x&&tt.y===v.y&&Eo(X)},{I:!0,A:!0});Ot(a,{x:0,y:0}),y();const $=ht(a),L=He(a);Ot(a,{x:L.w,y:L.h});const N=ht(a);Ot(a,{x:N.x-$.x<1&&-L.w,y:N.y-$.y<1&&-L.h});const G=ht(a);return Ot(a,v),pn(()=>w()),{D:$,M:G}},T=(p,b)=>{const v=vt.devicePixelRatio%1!==0?1:0,y={w:i(p.w-b.w),h:i(p.h-b.h)};return{w:y.w>v?y.w:0,h:y.h>v?y.h:0}},H=(p,b)=>{const v=(y,w,$,L)=>{const N=y===kt?wt:Rs(y),G=Wt(y),X=Wt($);return!w&&!L?wt:G&&X?kt:G?w&&L?N:w?kt:wt:w?N:X&&L?kt:wt};return{x:v(b.x,p.x,b.y,p.y),y:v(b.y,p.y,b.x,p.x)}},I=p=>{const b=y=>[kt,wt,Xt].map(w=>h(fn(w),y)),v=b(!0).concat(b()).join(" ");l(v),l(xt(p).map(y=>h(p[y],y==="x")).join(" "),!0)},[V,k]=yt(M,D(On,c)),[m,O]=yt(M,D(He,c)),[_,z]=yt(M),[F]=yt(P),[W,Y]=yt(M),[B]=yt(P),[K]=yt({i:(p,b)=>Fe(p,b,A),o:{}},()=>ys(c)?$t(c,A):{}),[U,ot]=yt({i:(p,b)=>Ce(p.D,b.D)&&Ce(p.M,b.M),o:Ao()}),E=ie(Po),h=(p,b)=>`${b?ws:Ss}${rs(p)}`;return({zt:p,Qt:b,un:v,It:y},{_n:w})=>{const{_t:$,Ht:L,xt:N,Ct:G,ft:X,Et:tt}=b||{},R=E&&E.R(t,e,v,n,p),{X:it,Y:dt,W:lt}=R||{},[ut,et]=zs(p,n),[st,ct]=p("overflow"),Z=Wt(st.x),at=Wt(st.y),ft=$||w||N||G||tt||et;let Zt=k(y),le=O(y),ae=z(y),ue=Y(y);if(et&&g&&l(Io,!ut),ft){Cn(s,Ht,Oe)&&q(!0);const kn=dt&&dt(),[he]=Zt=V(y),[ge]=le=m(y),be=Oo(c),me=S&&vs(d()),qo={w:i(ge.w+he.w),h:i(ge.h+he.h)},Mn={w:i((me?me.w:be.w+i(be.w-ge.w))+he.w),h:i((me?me.h:be.h+i(be.h-ge.h))+he.h)};kn&&kn(),ue=W(Mn),ae=_(T(qo,Mn),y)}const[de,fe]=ue,[Dt,Jt]=ae,[Be,je]=le,[We,Ye]=Zt,[mt,pe]=F({x:Dt.w>0,y:Dt.h>0}),Rt=Z&&at&&(mt.x||mt.y)||Z&&mt.x&&!mt.y||at&&mt.y&&!mt.x,Gt=w||G||tt||Ye||je||fe||Jt||ct||et||ft||L&&S,[ve,ye]=K(y),An=G||X||ye||pe||y,[Bo,jo]=An?U(x(ve),y):ot();let te=H(mt,st);q(!1),Gt&&(I(te),te=Vs(c,mt),lt&&it&&(it(te,Be,We),ce(c,lt(te))));const[Wo,Yo]=B(te);return De(s,Ht,Oe,Rt),De(o,un,Oe,Rt),j(e,{cn:Wo,Vt:{x:de.w,y:de.h},Rt:{x:Dt.w,y:Dt.h},j:mt,Dt:hs(Bo,Dt)}),{sn:Yo,tn:fe,nn:Jt,en:jo||Jt,dn:An}}},oc=t=>{const[e,n,s]=Gs(t),o={rn:{t:0,r:0,b:0,l:0},fn:!1,F:{[ao]:0,[uo]:0,[lo]:0,[so]:0,[co]:0,[io]:0,[ro]:0},Vt:{x:0,y:0},Rt:{x:0,y:0},cn:{x:wt,y:wt},j:{x:!1,y:!1},Dt:Ao()},{dt:c,gt:r,V:u,St:a}=e,{U:f,k:l}=Et(),d=!f&&(l.x||l.y),g=[tc(e),ec(e,o),nc(e,o)];return[n,S=>{const i={},A=d&&ht(r),M=A&&a();return Q(g,P=>{j(i,P(S,i)||{})}),Ot(r,A),M&&M(),u||Ot(c,0),i},o,e,s]},sc=(t,e,n,s,o)=>{let c=!1;const r=Jn(e,{}),[u,a,f,l,d]=oc(t),[g,S,i]=qs(l,f,r,x=>{q({},x)}),[C,A,,M]=Js(t,e,i,f,l,o),P=x=>xt(x).some(T=>!!x[T]),q=(x,T)=>{if(n())return!1;const{pn:H,It:I,Tt:V,vn:k}=x,m=H||{},O=!!I||!c,_={zt:Jn(e,m,O),pn:m,It:O};if(k)return A(_),!1;const z=T||S(j({},_,{Tt:V})),F=a(j({},_,{un:i,Qt:z}));A(j({},_,{Qt:z,Zt:F}));const W=P(z),Y=P(F),B=W||Y||!wn(m)||O;return c=!0,B&&s(x,{Qt:z,Zt:F}),B};return[()=>{const{ln:x,gt:T,St:H}=l,I=ht(x),V=[g(),u(),C()],k=H();return Ot(T,I),k(),D(gt,V)},q,()=>({gn:i,hn:f}),{bn:l,wn:M},d]},Tn=new WeakMap,cc=(t,e)=>{Tn.set(t,e)},rc=t=>{Tn.delete(t)},Uo=t=>Tn.get(t),It=(t,e,n)=>{const{tt:s}=Et(),o=Ae(t),c=o?t:t.target,r=Uo(c);if(e&&!r){let u=!1;const a=[],f={},l=m=>{const O=vo(m),_=ie(bs);return _?_(O,!0):O},d=j({},s(),l(e)),[g,S,i]=an(),[C,A,M]=an(n),P=(m,O)=>{M(m,O),i(m,O)},[q,x,T,H,I]=sc(t,d,()=>u,({pn:m,It:O},{Qt:_,Zt:z})=>{const{_t:F,Ct:W,$t:Y,xt:B,Ht:K,ft:U}=_,{tn:ot,nn:E,sn:h,en:p}=z;P("updated",[k,{updateHints:{sizeChanged:!!F,directionChanged:!!W,heightIntrinsicChanged:!!Y,overflowEdgeChanged:!!ot,overflowAmountChanged:!!E,overflowStyleChanged:!!h,scrollCoordinatesChanged:!!p,contentMutation:!!B,hostMutation:!!K,appear:!!U},changedOptions:m||{},force:!!O}])},m=>P("scroll",[k,m])),V=m=>{rc(c),gt(a),u=!0,P("destroyed",[k,m]),S(),A()},k={options(m,O){if(m){const _=O?s():{},z=zo(d,j(_,l(m)));wn(z)||(j(d,z),x({pn:z}))}return j({},d)},on:C,off:(m,O)=>{m&&O&&A(m,O)},state(){const{gn:m,hn:O}=T(),{B:_}=m,{Vt:z,Rt:F,cn:W,j:Y,rn:B,fn:K,Dt:U}=O;return j({},{overflowEdge:z,overflowAmount:F,overflowStyle:W,hasOverflow:Y,scrollCoordinates:{start:U.D,end:U.M},padding:B,paddingAbsolute:K,directionRTL:_,destroyed:u})},elements(){const{dt:m,vt:O,rn:_,L:z,ht:F,gt:W,Kt:Y}=H.bn,{jt:B,Jt:K}=H.wn,U=E=>{const{Mt:h,Ut:p,Lt:b}=E;return{scrollbar:b,track:p,handle:h}},ot=E=>{const{Xt:h,Yt:p}=E,b=U(h[0]);return j({},b,{clone:()=>{const v=U(p());return x({vn:!0}),v}})};return j({},{target:m,host:O,padding:_||z,viewport:z,content:F||z,scrollOffsetElement:W,scrollEventElement:Y,scrollbarHorizontal:ot(B),scrollbarVertical:ot(K)})},update:m=>x({It:m,Tt:!0}),destroy:D(V,!1),plugin:m=>f[xt(m)[0]]};return nt(a,[I]),cc(c,k),Do(ko,It,[k,g,f]),Ks(H.bn.bt,!o&&t.cancel)?(V(!0),k):(nt(a,q()),P("initialized",[k]),k.update(),k)}return r};It.plugin=t=>{const e=St(t),n=e?t:[t],s=n.map(o=>Do(o,It)[0]);return gs(n),e?s:s[0]};It.valid=t=>{const e=t&&t.elements,n=Ct(e)&&e();return Te(n)&&!!Uo(n.target)};It.env=()=>{const{P:t,k:e,U:n,J:s,ot:o,st:c,K:r,Z:u,tt:a,nt:f}=Et();return j({},{scrollbarsSize:t,scrollbarsOverlaid:e,scrollbarsHiding:n,scrollTimeline:s,staticDefaultInitialization:o,staticDefaultOptions:c,getDefaultInitialization:r,setDefaultInitialization:u,getDefaultOptions:a,setDefaultOptions:f})};It.nonce=Bs;It.trustedTypePolicy=ds;const ic={class:"text-center"},lc={key:0},ac=["onClick"],uc={class:"version-title"},dc={class:"flex items-center justify-between"},fc={class:"version-date"},pc={class:"version-content transition-all"},vc={style:{"white-space":"pre"}},yc={key:1},hc={class:"version-title"},gc={class:"flex items-center justify-between"},bc={class:"version-date"},mc={class:"version-content transition-all"},wc={style:{"white-space":"pre-wrap"}},Sc={__name:"updateHistory",props:{isShowList:!1},setup(t,{expose:e}){const n=()=>{c.value=!0},s=t,o=qe([]),c=qe(!1);let r;async function u(){if(s.isShowList){const g=await In();g.code===1&&(o.value=g.data);return}else{if((sessionStorage.getItem("showHistory")||0)>0)return;const S=await es();if(S.code!==1||S.data.version===localStorage.getItem("version"))return;{const i=await In();c.value=!0,i.code===1&&(o.value=i.data.map(C=>({...C,open:!0}))),r=setTimeout(()=>{sessionStorage.setItem("showHistory",1)},2e3)}}}u();const a=qe(null);let f=null;const l=()=>{c.value=!1};function d(g){o.value[g].open=!o.value[g].open,ns(()=>{f&&f.update()})}return Ko(()=>{f=It(a.value,{scrollbars:{autoHide:"leave",theme:"os-theme-dark"},overflow:{x:"hidden"}})}),Qo(()=>{clearTimeout(r),f&&(f.destroy(),f=null)}),e({open:n}),(g,S)=>Dn((Nt(),Vt("div",{onClick:l,class:"position-fixed top-0 left-0 w-100vw h-100vh bg-gray-100/50 flex justify-center items-center"},[rt("div",{onClick:ts(()=>{},["stop"]),class:"changelog-container shadow-gray-300/50"},[rt("div",{ref_key:"scrollEl",ref:a,class:"changelog os-host"},[S[1]||(S[1]=rt("h1",null,"更新日志",-1)),rt("div",{class:"close-btn",onClick:l},"关闭"),Dn(rt("div",ic,"暂无更新日志",512),[[Hn,o.value.length==0]]),s.isShowList?(Nt(),Vt("div",lc,[(Nt(!0),Vt(Zo,null,Jo(o.value,(i,C)=>(Nt(),Vt("div",{key:C,class:we(["version-block",{open:i.open}])},[rt("div",{class:"version-header",onClick:A=>d(C)},[rt("div",uc,Ft(i.version),1),rt("div",dc,[rt("span",fc,Ft(i.date),1),rt("span",{class:we(["toggle-icon inline-block ml-2",{"rotate-40":i.open}])},"▶",2)])],8,ac),rt("div",pc,[rt("ul",null,[rt("li",vc,Ft(i.ver_intro),1)])])],2))),128))])):(Nt(),Vt("div",yc,[o.value.length>0?(Nt(),Vt("div",{key:0,class:we(["version-block",{open:o.value[0].open}])},[rt("div",{class:"version-header",onClick:S[0]||(S[0]=i=>d(0))},[rt("div",hc,Ft(o.value[0].version),1),rt("div",gc,[rt("span",bc,Ft(o.value[0].date),1),rt("span",{class:we(["toggle-icon inline-block ml-2",{"rotate-40":o.value[0].open}])},"▶",2)])]),rt("div",mc,[rt("ul",null,[rt("li",wc,Ft(o.value[0].ver_intro),1)])])],2)):Go("",!0)]))],512)])],512)),[[Hn,c.value]])}},Cc=Xo(Sc,[["__scopeId","data-v-f0a1d45d"]]);export{Cc as U};
//# sourceMappingURL=updateHistory-BiwdgKhC.js.map
