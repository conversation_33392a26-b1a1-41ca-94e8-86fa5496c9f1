"use strict";(function(){try{var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};r.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new r.Error().stack;e&&(r._sentryDebugIds=r._sentryDebugIds||{},r._sentryDebugIds[e]="dcc3571f-0f0e-4109-b2d6-a78a5a6687d0",r._sentryDebugIdIdentifier="sentry-dbid-dcc3571f-0f0e-4109-b2d6-a78a5a6687d0")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});require("electron");require("idb");require("crypto-js");require("dayjs");const d=require("./platfrom.js");class c extends d.Platform{constructor(e){super(e)}async getUserId(){if(!this.userId){let e=await cookieStore.get("c_user");if(e){this.userId=e.value;let t={userId:this.userId,platform:this.platform,phone:this.userId,nickName:this.userName||this.userId,session_id:this.viewSessionId};this.sendPlatformUserInfo(t)}}}userId;chatUserId;userName;async init(e){await super.init(e),this.getUserId(),this.bindInputFunction()}_o(e,t){/\/latest\/inbox\//.test(location.pathname)&&(this.bindInputFunction(),this.translateList(),this.getUnreadSessionUserToFans(),this.getUserId(),this.getCurrentSessionUserId()),this.getUserName()}_u(e){this.getCurrentSessionUserId()}getUserName(){if(!this.userName){let e=document.querySelector(this.sI.userNameSelector);if(e&&this.userId){if(!e.innerText)return;this.userName=e.innerText;let t={userId:this.userId,platform:this.platform,phone:this.userId,nickName:this.userName||this.userId,session_id:this.viewSessionId};this.sendPlatformUserInfo(t)}}}getCurrentSessionUserId(){let e=0;const t=()=>{let s=document.querySelector(this.sI.currentSessionUserNameElSelector);if(s&&e<5){if(this.chatUserId!==s.innerText)this.chatUserId=s.innerText;else return;let i={mainAccount:this.userId,fansId:this.chatUserId,nickName:this.chatUserId,platform:this.platform};this.sendCurrentSessionFansInfo(i)}else setTimeout(()=>{e++,window.requestAnimationFrame(t)},500)};window.requestAnimationFrame(t)}getUnreadSessionUserToFans(){const e=document.querySelectorAll(this.sI.sessionElSelector);let t=0;const s=[];e.forEach(i=>{const n=i.querySelector(this.sI.unreadElSelector);if(n){if(t+=1,n.hasAttribute("aira-isread"))return;{n.setAttribute("aira-isread","true");let o=i.querySelector(this.sI.sessionUserNameElSelector)?.innerText;s.push({id:o,nickName:o})}}}),t>0&&this.sendUnReadCount(t),s.length>0&&this.sendNewFansList({viewSessionId:this.viewSessionId,platform:this.platform,mainAccount:this.userId,unreadListInfo:s})}bindInputFunction(){const e=t=>{const s=document.querySelector(this.sI.inputElSelector);s?(s.addEventListener("keydown",async i=>{if(s.setAttribute("aria-bind","true"),(i.key==="Enter"||i.keyCode===13)&&!i.shiftKey){console.log(i),i.stopPropagation(),i.stopImmediatePropagation(),this.changeInputEditStatus(!1,this.sI.inputElSelector);let n=this.translater.config;const o=n&&n.trans_over,l=i.target.value;if(o&&l.trim()){let a=await this.translater.translateInput(l);console.log("tv",a),a?await this.inputMessage(a,!0):await this.inputMessage(l,!1)}else await this.inputMessage(l,!0);this.switchShadowState(!1)}},{capture:!0},!0),s.addEventListener("input",i=>{this.createMaskDiv()}),this.createMaskDiv()):setTimeout(()=>{window.requestAnimationFrame(e)},500)};window.requestAnimationFrame(e)}sendMessageToInput({type:e,message:t}){this.inputMessage(t,e==="send")}inputMessage(e,t=!1){return this.changeInputEditStatus(!0,this.sI.inputElSelector),new Promise(s=>{let i=document.querySelector(this.sI.inputElSelector);i&&(i.focus(),i.setRangeText(e,0,-1),i.dispatchEvent(new Event("input",{bubbles:!0,data:e,inputType:"insertText"})),t&&setTimeout(()=>{document.querySelector(this.sI.sendButtonElSelector)?.dispatchEvent(new MouseEvent("click",{bubbles:!0,view:window})),s()},50))})}switchShadowState(e){let t=document.querySelector("#myShadow");t&&(e?t.style.display="block":t.style.display="none")}createMaskDiv(){if(document.querySelector("#myShadow"))this.switchShadowState(!0);else{let t=document.createElement("div");t.id="myShadow",t.style.position="absolute",t.style.width="68px",t.style.height="40px",t.style.top="0px",t.style.right="0px",t.style.zIndex=9999;let s=document.querySelector(this.sI.sendButtonContainerElSelector);if(s)s.style.position="relative",s.appendChild(t);else return;t.addEventListener("click",async i=>{i.stopPropagation(),this.changeInputEditStatus(!1,this.sI.inputElSelector);let n=this.translater.config;const o=n&&n.trans_over,a=document.querySelector(this.sI.inputElSelector)?.value;if(console.log(a),o&&a.trim()){let u=await this.translater.translateInput(a);u?await this.inputMessage(u,!0):await this.inputMessage(a,!1)}else await this.inputMessage(a,!0);this.switchShadowState(!1)})}}changeInputEditStatus(e,t){try{let s=document.querySelector(t);if(!s)return;e?s.removeAttribute("disabled"):s.setAttribute("disabled","true")}catch(s){console.error(s)}}async translateList(){let e=document.querySelector(this.sI.messageListElSelector);e&&(e.querySelectorAll(this.sI.reciveMessageElSelector).forEach((i,n)=>{this.translater.translateMessage(i,{type:"in"})}),e.querySelectorAll(this.sI.sendMessageElSelector).forEach((i,n)=>{this.translater.translateMessage(i,{type:"out"})}))}}exports.FacebookBusinessHandler=c;
//# sourceMappingURL=facebookBusiness.js.map
