package backend

import (
	"embed"
	"fmt"
	"io/fs"
	"log"
	"os"
	"path/filepath"
)

var AppName = "MyApp"
var InstallDir = filepath.Join(os.Getenv("LOCALAPPDATA"), AppName)
var UninstallArg = "--uninstall"

// CopyExtrasFiles 复制extras目录下的所有文件到安装目录
func CopyExtrasFiles(extrasFS embed.FS, installDir string) error {
	log.Printf("开始复制extras文件到: %s", installDir)

	// 遍历extras目录下的所有文件
	err := fs.WalkDir(extrasFS, "extras", func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		// 跳过目录本身
		if d.IsDir() {
			return nil
		}

		// 计算相对路径（去掉extras前缀）
		relPath, err := filepath.Rel("extras", path)
		if err != nil {
			return err
		}

		// 目标文件路径
		dstPath := filepath.Join(installDir, relPath)

		// 确保目标目录存在
		dstDir := filepath.Dir(dstPath)
		if err := os.MkdirAll(dstDir, 0755); err != nil {
			return err
		}

		// 读取嵌入文件内容
		content, err := extrasFS.ReadFile(path)
		if err != nil {
			return err
		}

		// 写入目标文件
		if err := os.WriteFile(dstPath, content, 0644); err != nil {
			return err
		}

		log.Printf("复制文件: %s -> %s", path, dstPath)
		return nil
	})

	if err != nil {
		return fmt.Errorf("复制extras文件失败: %v", err)
	}

	log.Printf("extras文件复制完成")
	return nil
}

// 全局变量用于存储extras文件系统
var ExtrasFS *embed.FS

func InstallApp() (string, error) {
	log.Printf("开始安装应用: %s", AppName)
	log.Printf("安装目录: %s", InstallDir)

	// 创建安装目录
	if err := os.MkdirAll(InstallDir, 0755); err != nil {
		return "", fmt.Errorf("创建安装目录失败: %v", err)
	}

	// 复制主程序
	exePath, _ := os.Executable()
	dstExe := filepath.Join(InstallDir, filepath.Base(exePath))
	log.Printf("复制主程序: %s -> %s", exePath, dstExe)
	if err := CopyFile(exePath, dstExe); err != nil {
		return "", fmt.Errorf("复制主程序失败: %v", err)
	}

	// 复制extras文件（如果ExtrasFS已设置）
	if ExtrasFS != nil {
		if err := CopyExtrasFiles(*ExtrasFS, InstallDir); err != nil {
			return "", fmt.Errorf("复制extras文件失败: %v", err)
		}
	}

	// 创建快捷方式
	lnkPath := filepath.Join(InstallDir, "卸载 "+AppName+".lnk")
	log.Printf("创建卸载快捷方式: %s", lnkPath)
	if err := CreateShortcut(lnkPath, dstExe, UninstallArg, InstallDir); err != nil {
		return "", fmt.Errorf("创建快捷方式失败: %v", err)
	}

	// 写注册表卸载信息
	uninstallCmd := fmt.Sprintf("\"%s\" %s", dstExe, UninstallArg)
	log.Printf("添加注册表卸载信息")
	if err := AddUninstallInfo(AppName, InstallDir, uninstallCmd); err != nil {
		return "", fmt.Errorf("添加注册表信息失败: %v", err)
	}

	log.Printf("安装完成！")
	return "安装完成！", nil
}
