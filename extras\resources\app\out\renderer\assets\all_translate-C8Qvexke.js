import{u as j,r as v,w as K,f as u,h as l,i as f,C as E,D as m,F as p,B as h,n as b,j as _,Z as X,Y as G,O as J,m as O,p as o,a4 as L,A as g,y as A,E as y}from"./index-BO8ZgokY.js";import{t as U}from"./translate-DZ1BFMsZ.js";(function(){try{var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};r.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},w=new r.Error().stack;w&&(r._sentryDebugIds=r._sentryDebugIds||{},r._sentryDebugIds[w]="d0c374af-b583-4e21-9ddc-1d43c3442ed3",r._sentryDebugIdIdentifier="sentry-dbid-d0c374af-b583-4e21-9ddc-1d43c3442ed3")})()}catch{}const P={class:"sys_base"},$={class:"all_translate_cont"},ee={class:"tit"},le={class:"tit_open"},te={class:"set_all_translate"},se={class:"trans_left"},ne={class:"lang_translage"},ae={class:"select_info"},oe={class:"trans"},ie=["src"],ue={class:"select_info"},de={class:"trans_info"},ce={class:"line_set"},re={class:"all_tit"},_e=["src"],ge={class:"items"},Ae=["onClick"],ve=["src"],fe={class:"line_set"},me={class:"all_tit"},pe=["src"],he={class:"items"},ye=["onClick"],we=["src"],Ce={class:"trans_right"},be={class:"items"},Se={key:0,class:"item"},Ee={class:"tit"},Ve=["onClick","src"],Ie={class:"trans_c"},Re={key:0},ke={class:"items"},Le={key:0,class:"item"},Ue={class:"tit"},Be=["onClick","src"],ze={class:"trans_c"},Ye={key:0},De={__name:"all_translate",setup(r){const w=j();J();const B=O(),S=new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAMCAYAAABSgIzaAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABuSURBVHgBnY/RDYAgDERJ/PGTEdhEN9INcANHcQRGcZTzjP2oCUrLJRegx0vbEBwCEOnJw9xQok88WgYrxKPQSUqjCVKdIPfUD0l40NnViY+iwmwej4UVb+2w7sRgQ13f0A/chiqwHVLwTMfWvwtnWvkR+Idk5gAAAABJRU5ErkJggg==",import.meta.url).href,z=new URL("data:image/svg+xml,%3csvg%20width='37'%20height='38'%20viewBox='0%200%2037%2038'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M20.2596%2017.5554C20.4313%2017.5554%2020.5988%2017.6086%2020.739%2017.7076L29.6298%2023.988C30.0047%2024.2529%2030.0939%2024.7714%2029.8291%2025.1463C29.7781%2025.2185%2029.7159%2025.2821%2029.6448%2025.3347L20.7541%2031.9172C20.3852%2032.1903%2019.8647%2032.1127%2019.5916%2031.7438C19.4857%2031.6007%2019.4285%2031.4273%2019.4285%2031.2493V27.0147H12.16C6.98003%2027.0442%204.16723%2024.4842%203.72168%2019.3349C4.17179%2019.8061%206.13904%2022.5868%2012.16%2022.5868C16.174%2022.5868%2018.5969%2022.5407%2019.4285%2022.4485V18.3864C19.4285%2017.9274%2019.8006%2017.5554%2020.2596%2017.5554ZM14.6984%204.81069C14.8043%204.95379%2014.8615%205.12714%2014.8615%205.3052V9.53975H22.13C27.31%209.5103%2030.1228%2012.0703%2030.5684%2017.2196C30.1183%2016.7484%2028.151%2013.9677%2022.13%2013.9677C18.116%2013.9677%2015.6932%2014.0138%2014.8615%2014.106V18.168C14.8615%2018.627%2014.4895%2018.9991%2014.0305%2018.9991C13.8588%2018.9991%2013.6913%2018.9459%2013.551%2018.8468L4.66019%2012.5665C4.28532%2012.3016%204.1961%2011.7831%204.46092%2011.4082C4.51193%2011.336%204.57414%2011.2724%204.6452%2011.2197L13.536%204.63732C13.9049%204.36419%2014.4253%204.44181%2014.6984%204.81069Z'%20fill='%232072F7'/%3e%3c/svg%3e",import.meta.url).href,I=new URL("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAWCAYAAADEtGw7AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADGSURBVHgB1ZTLEYIwEIb/dbjYRUqwBOzEFqQBYwFKS5RACSnBG97WBcXHkMesE2bgO/DIJF9gs7vA2qDxwVT3Gsw7KHH1du8bLz5bDNISmSimQ3wDURtdxTByNToxU+uu/t8bMVV3kpvViZXIJvx+YTRjzDeYiRxiK1/qsovlPM5icdnFIdYnnqYbwbzyNAxLhRKU4mdF2diilLRn/lC4y28Zf1WUHVLqX3EQxsEcuzI+p++MpBRTupP5gl5E5jZQk2i3i+YBnoUu3Ibi6woAAAAASUVORK5CYII=",import.meta.url).href,n=v({pt:[],zy:[],ptAllSel:!1,zyAllSel:!1}),Y=()=>{n.value.ptAllSel=!n.value.ptAllSel;let t=n.value.pt;for(let e=0;e<t.length;e++)t[e].sel=n.value.ptAllSel;n.value.pt=t},x=t=>{let e=n.value.pt;e[t].sel=!e[t].sel,n.value.pt=e;let d=!0;for(let a=0;a<e.length;a++)e[a].sel||(d=!1);n.value.ptAllSel=d},T=()=>{n.value.zyAllSel=!n.value.zyAllSel;let t=n.value.zy;for(let e=0;e<t.length;e++)t[e].sel=n.value.zyAllSel;n.value.zy=t},D=t=>{let e=n.value.zy;e[t].sel=!e[t].sel,n.value.zy=e;let d=!0;for(let a=0;a<e.length;a++)e[a].sel||(d=!1);n.value.zyAllSel=d},R=async t=>{try{await navigator.clipboard.writeText(t),y({showClose:!0,message:"复制成功",type:"success"})}catch{y({showClose:!0,message:"复制失败",type:"info"})}};v({}),v(!1);const i=v({}),V=v({});function N(){X({}).then(t=>{t.code==1&&(V.value=t.data)})}N();const Q=v({});function F(){G({}).then(t=>{if(t.code==1){Q.value=t.data;let e=[],d=[];for(let a=0;a<t.data.length;a++)t.data[a].engine_type=="general"&&e.push(t.data[a]),t.data[a].engine_type=="profession"&&d.push(t.data[a]);n.value.pt=e,n.value.zy=d}})}F();const H=()=>{let t=n.value.pt;if(!i.value.to_lang){y({showClose:!0,message:"请选择翻译语言",type:"warning"});return}let e=!1;for(let a=0;a<t.length;a++)t[a]&&t[a].sel&&(e=!0,U({engineCode:t[a].engine_code,fromLang:i.value.from_lang,toLang:i.value.to_lang,text:i.value.desc}).then(c=>{c.code==1?t[a].transInfo=c.data.result:c.code==400010?y({showClose:!0,message:c.msg,type:"warning"}):y({showClose:!0,message:c.msg==="翻译语言编码不能为空"?"请设置翻译语言":"翻译失败，请稍后重试",type:"warning"})}));n.value.pt=t;let d=n.value.zy;for(let a=0;a<t.length;a++)d[a]&&d[a].sel&&(e=!0,U({engineCode:d[a].engine_code,fromLang:i.value.from_lang,toLang:i.value.to_lang,text:i.value.desc}).then(c=>{c.code==1&&(d[a].transInfo=c.data.result)}));n.value.zy=d,e||y({showClose:!0,message:"请至少选择一条翻译线路",type:"warning"})},q=v(!0);return K(()=>B.query,(t,e)=>{t.chat=="slide"&&(q.value=t.type=="true")},{immediate:!0}),(t,e)=>{const d=m("el-switch"),a=m("el-form-item"),c=m("el-option"),k=m("el-select"),M=m("el-input"),W=m("el-form");return o(),u("div",P,[l("div",{class:b(_(w).showHideWord?"main_cont all_translate_main_cont":"main_cont all_translate_main_cont small_tab")},[l("div",$,[l("div",ee,[e[4]||(e[4]=l("div",{class:"tit_info"},[l("h2",null,"聚合翻译"),l("span",null,"Aggregate Translation")],-1)),l("div",le,[f(a,{label:"反向翻译对照"},{default:E(()=>[f(d,{modelValue:i.value.self,"onUpdate:modelValue":e[0]||(e[0]=s=>i.value.self=s),"active-value":"true","inactive-value":"false"},null,8,["modelValue"])]),_:1})])]),l("div",te,[l("div",se,[f(W,{ref:"form",model:i.value},{default:E(()=>[l("div",ne,[l("div",ae,[f(k,{modelValue:i.value.from_lang,"onUpdate:modelValue":e[1]||(e[1]=s=>i.value.from_lang=s),placeholder:"输入语言"},{default:E(()=>[(o(!0),u(p,null,h(V.value,s=>(o(),L(c,{label:s.lang_name,value:s.lang_code},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),l("div",oe,[l("img",{src:_(z),alt:""},null,8,ie)]),l("div",ue,[f(k,{modelValue:i.value.to_lang,"onUpdate:modelValue":e[2]||(e[2]=s=>i.value.to_lang=s),placeholder:"翻译语言"},{default:E(()=>[(o(!0),u(p,null,h(V.value,s=>(o(),L(c,{label:s.lang_name,value:s.lang_code},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])])]),l("div",de,[f(M,{type:"textarea",modelValue:i.value.desc,"onUpdate:modelValue":e[3]||(e[3]=s=>i.value.desc=s)},null,8,["modelValue"])]),l("div",ce,[l("div",re,[l("div",{class:b(n.value.ptAllSel?"item on":"item"),onClick:Y},[l("p",null,[n.value.ptAllSel?(o(),u("img",{key:0,src:_(S),alt:""},null,8,_e)):g("",!0)]),e[5]||(e[5]=l("span",null,"普通线路",-1))],2)]),l("div",ge,[(o(!0),u(p,null,h(n.value.pt,(s,C)=>(o(),u("div",{onClick:Z=>x(C),class:b(s.sel?"item on":"item")},[l("p",null,[s.sel?(o(),u("img",{key:0,src:_(S),alt:""},null,8,ve)):g("",!0)]),l("span",null,A(s.engine_name),1)],10,Ae))),256))])]),l("div",fe,[l("div",me,[l("div",{class:b(n.value.zyAllSel?"item on":"item"),onClick:T},[l("p",null,[n.value.zyAllSel?(o(),u("img",{key:0,src:_(S),alt:""},null,8,pe)):g("",!0)]),e[6]||(e[6]=l("span",null,"专业线路",-1))],2)]),l("div",he,[(o(!0),u(p,null,h(n.value.zy,(s,C)=>(o(),u("div",{onClick:Z=>D(C),class:b(s.sel?"item on":"item")},[l("p",null,[s.sel?(o(),u("img",{key:0,src:_(S),alt:""},null,8,we)):g("",!0)]),l("span",null,A(s.engine_name),1)],10,ye))),256))])]),l("div",{class:"trans_btn",onClick:H},"翻译")]),_:1},8,["model"])]),l("div",Ce,[(o(!0),u(p,null,h(n.value.pt,s=>(o(),u("div",be,[s.sel&&s.transInfo?(o(),u("div",Se,[l("div",Ee,[l("span",null,A(s.engine_name),1),l("img",{onClick:C=>R(s.transInfo),src:_(I),alt:""},null,8,Ve)]),l("div",Ie,[l("p",null,A(s.transInfo),1),i.value.self=="true"?(o(),u("p",Re,A(i.value.desc),1)):g("",!0)])])):g("",!0)]))),256)),(o(!0),u(p,null,h(n.value.zy,s=>(o(),u("div",ke,[s.sel&&s.transInfo?(o(),u("div",Le,[l("div",Ue,[l("span",null,A(s.engine_name),1),l("img",{onClick:C=>R(s.transInfo),src:_(I),alt:""},null,8,Be)]),l("div",ze,[l("p",null,A(s.transInfo),1),i.value.self=="true"?(o(),u("p",Ye,A(i.value.desc),1)):g("",!0)])])):g("",!0)]))),256))])])])],2)])}}};export{De as default};
//# sourceMappingURL=all_translate-C8Qvexke.js.map
