"use strict";(function(){try{var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};r.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},t=new r.Error().stack;t&&(r._sentryDebugIds=r._sentryDebugIds||{},r._sentryDebugIds[t]="dd2c1380-c5e4-484d-893d-cdf49fe6f579",r._sentryDebugIdIdentifier="sentry-dbid-dd2c1380-c5e4-484d-893d-cdf49fe6f579")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const I=require("electron"),b=require("../platfrom.js"),E=require("../../utils/performance.js"),w=require("./tools.js");class S extends b.Platform{constructor(t){super(t)}tools=new w.WhatsappTools;Store;userId;userName;newFansRecord="";chatUserId="";isMac=navigator.userAgentData?.platform==="macOS"||/Mac/.test(navigator.userAgent);async init(t){await super.init(t)}getStore(){try{if(this.Store)return;this.Store=window.require("WAWebCollections"),this.Store?.Msg.on("add",t=>{this.getNewFans(t),this.getChatUserId(),setTimeout(()=>{this.translateList()},1e3)}),console.group("require"),console.log(this.Store),console.groupEnd()}catch(t){console.warn(t)}}_o(t,e){this.getUserInfo(),this.attBindInputKeydown(),this.bindInputKeydown(),this.setupDelegatedKeydownListener(),this.autoReplyHandler(),this.proxyListDOM()}showTranslateContrast(){const t=document.querySelector("._ak1r");if(t&&this.translater.config.isContrast){const e=t.getBoundingClientRect();this.translateContrast.setRect({top:`${e.top-e.height}px`,left:`${e.left}px`,width:`${e.width}px`,height:`${e.height}px`,paddingLeft:"12px",paddingRight:"12px",boxSizing:"border-box"}),this.translateContrast.show();return}this.translateContrast.hide()}_isDelegatedListenerAttached=!1;_u(t){this.getStore(),this.setupDelegatedKeydownListener()}getActiveChat(){return!this.Store||!this.Store.Chat?null:this.Store.Chat._models.find(t=>t.active)}async _handleKeydownEvent(t){if(this.isComposing)return;const e=t.target.closest(this.sI.input);this.showTranslateContrast(),e&&(this.setMaskBtn("mask-btn",this.sI.maskDependEl),this.setMaskBtn("mask-btn",this.sI.maskDependIsMerchantEl),(t.key==="Enter"||t.keyCode===13)&&!t.shiftKey?(t.preventDefault(),t.stopPropagation(),t.stopImmediatePropagation(),this.sendMessage()):t.key==="Backspace"||t.key==="Delete"?(setTimeout(()=>{e.innerText.trim()===""&&(this.translateContrast.setTextContent(),this.translateContrast.hide())},10),this._handleInputEvent(t)):this._handleInputEvent(t))}setupDelegatedKeydownListener(){this._isDelegatedListenerAttached||(document.addEventListener("keydown",this._handleKeydownEvent.bind(this),!0),document.addEventListener("compositionend",t=>{this.isComposing=!1,this._handleInputEvent(t)}),document.addEventListener("compositionstart",()=>{this.isComposing=!0}),this._isDelegatedListenerAttached=!0,console.log("Delegated keydown listener has been set up."))}_handleInputEvent=E.debounce(async t=>{const e=t.target.closest(this.sI.input);if(e&&e.innerText.trim()){const s=await this.translater.translateInput(e.innerText);this.translateContrast.setTextContent(s)}},300);watchBodyScroll(){let t=document.querySelector(this.sI.messageListElSelector).parentElement;t&&!t.onscrollend&&(t.onscrollend=()=>{this.translateList()})}observerMessageList(){this.mutationObserver.observerFactory(document.querySelector(this.sI.messageListElSelector),{subtree:!0,childList:!0,attributes:!1,characterData:!1})}proxyListDOM(){[{target:document.querySelector(this.sI.chatListContainerSelector||"#pane-side"),closesetEl:this.sI.chatList},{target:document.querySelector(this.sI.searchListSelector||'span[class="x10l6tqk x13vifvy xtijo5x x1ey2m1c x1o0tod"]'),closesetEl:'[role="listitem"]'}].forEach(e=>{e.target&&!e.target.onclick&&(e.target.onclick=s=>{s.target.closest(e.closesetEl)&&(this.getChatUserId(),setTimeout(()=>{this.watchBodyScroll(),this.translateList()},1e3))})})}autoReplyHandler(){this.aiReply(async()=>{const t=document.querySelectorAll(this.sI.chatList),e=Array.from(t).filter(s=>s.querySelector(this.sI.unread)&&s.querySelector(this.sI.unread)?.innerText!=="0");if(e.length>0)for(const s of e){const n=s.closest(this.sI.chatList)?.getBoundingClientRect();await this.clickAt({x:Math.round(n.left+n.width/2),y:Math.round(n.top+n.height/2)}),s.querySelector("[aria-selected]>div").dispatchEvent(new Event("focus",{bubbles:!0,cancelable:!0})),await(async()=>new Promise(async(a,c)=>{setTimeout(async()=>{const o=s?.getBoundingClientRect();this.scrollToBottom(),await this.clickAt({x:Math.round(o.left+o.width/2),y:Math.round(o.top+o.height/2)}),document.querySelector(this.sI.input).click(),document.querySelector(this.sI.input).focus();let y=Array.from(document.querySelectorAll("[data-originaltext]")),u="",g=y.slice(-5).map(f=>{let m=f.innerText.split(`
`)[0],d="";return f.closest(this.sI.sendMessageElSelector)?(d="user",u=m):d="assistant",{content:m,role:d}}),h=await this.getAiReply({question:u,messageList:g,chat_id:this.userId});h&&await this.sendMessageEvent({text:h.content,is_send:!0,input:this.sI.input,btn:'[data-icon="wds-ic-send-filled"]'}),this.scrollToBottom();let p=document.querySelector('[role="grid"]');p?.click();const l=p?.getBoundingClientRect();await this.clickAt({x:Math.round(l.left+l.width/2),y:Math.round(l.top+l.height/2)}),s.querySelector(this.sI.unread)&&(s.querySelector(this.sI.unread).innerText="0"),setTimeout(()=>{a()},500)},1e3)}))()}})}sendMessageToInput(t){this.sendMessageEvent({text:t.message,is_send:t.type==="send",input:this.sI.input,btn:this.sI.sendBtn})}async getUserInfo(){if(!this.userId||!this.userName){let t=localStorage.getItem(this.sI.userInfoKey);if(console.log("获取到ws账号信息1",t),t){let n=t.replace(/\"/g,"").split(":")[0];if(n){this.userId=n;const i=await I.ipcRenderer.invoke("getWhatsappNameKey",n);let a=localStorage.getItem(i);a&&(this.userName=a.match(/"(.*)"/)[1]);let c={userId:n,platform:this.platform,phone:n,nickName:this.userName||n,session_id:this.viewSessionId};console.log("获取到ws账号信息2",c),this.sendPlatformUserInfo(c)}}}}getIsMerchant(){return!!document.querySelector(this.sI.dividerElSelector)}updateUserInfo(){if(this.userId){let t=null;t=this.getIsMerchant()?this.sI.shopOwnersNickNameElSelector:this.sI.nickNameElSelector;const e=document.querySelector(t);if(e&&!this.userName){this.userName=e.innerText;let s={userId:this.userId,platform:this.platform,phone:this.userId,nickName:this.userName||this.userId,session_id:this.viewSessionId};console.log("获取到ws账号信息3",s),this.sendPlatformUserInfo(s)}}}formatChatId(t){return t.replace(/\s/g,"").match(/\d+/g).join("")}getChatUserId(){const e=this.getActiveChat()?.id.user;let s=this.Store.Contact?._models.find(n=>n.id.user===e);if(e&&this.chatUserId!=e){this.chatUserId=e,console.log("当前正在聊天的用户id切换为：",this.chatUserId),this.setMaskBtn("mask-btn",this.sI.maskDependEl),this.setMaskBtn("mask-btn",this.sI.maskDependIsMerchantEl);let n={mainAccount:this.userId,fansId:this.chatUserId,nickName:s?.pushname??this.chatUserId,platform:this.platform};this.sendCurrentSessionFansInfo(n);return}}getNewFans(t){let e=t.id.user;if(t.isNewMsg&&this.userId!==e){const s=[];if(this.newFansRecord.indexOf(e)===-1){this.newFansRecord+=`,${e}`;const n=this.Store.Contact?._models.find(i=>i.id.user===e);s.push({nickName:n?.pushname||e,id:e})}s.length>0&&this.sendNewFansList({viewSessionId:this.viewSessionId,platform:this.platform,mainAccount:this.userId,unreadListInfo:s})}}bindInputKeydown(){const t=document.querySelector(this.sI.input);if(!t){setTimeout(()=>this.bindInputKeydown(),100);return}this._keydownHandler&&t.removeEventListener("keydown",this._keydownHandler,!0),this._keydownHandler=async e=>{this.setMaskBtn("mask-btn",this.sI.maskDependEl),this.setMaskBtn("mask-btn",this.sI.maskDependIsMerchantEl),(e.key==="Enter"||e.keyCode===13)&&!e.shiftKey&&(e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),this.sendMessage())},t.addEventListener("keydown",this._keydownHandler,!0)}async sendMessage(){this.setEditable(this.sI.input,!1);try{let t=document.querySelectorAll(this.sI.inputContent),e="";t.forEach(function(i){i&&(e+=i.innerText)});const s=this.translater?.config,n=s&&s.trans_over;if(e.trim()&&n){this.clearInput(this.sI.input);let i=await this.translater.translateInput(e);this.sendMessageEvent({text:i||e,is_send:!!i,input:this.sI.input,btn:this.sI.sendBtn})}else document.querySelector(this.sI.sendBtn)?.click(),document.querySelector(this.sI.sendBtnBusiness)?.click()}catch(t){console.log(t),this.setEditable(this.sI.input,!0)}this.setEditable(this.sI.input,!0)}attBindInputKeydown(){const t=document.querySelector(this.sI.attInput);if(t){if(t.getAttribute("data-att"))return;t.setAttribute("data-att","true"),t.addEventListener("keydown",async e=>{this.setMaskBtn("att-mask-btn",this.sI.attMaskDependEl),(e.key==="Enter"||e.keyCode===13)&&!e.shiftKey&&(e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),this.attSendMessage())},!0)}}async attSendMessage(){let t=document.querySelectorAll(this.sI.attInputContent),e="";t.forEach(function(i){i&&(e+=i.innerText)}),this.setEditable(this.sI.attInput,!1);const s=this.translater?.config,n=s&&s.trans_over;if(e.trim()&&n){this.clearInput(this.sI.attInput);let i=await this.translater.translateInput(e);this.sendMessageEvent({isAtt:!0,text:i||e,is_send:!!i,input:this.sI.attInput,btn:this.sI.attSendBtn})}else document.querySelector(this.sI.attSendBtn)?.click(),this.setEditable(this.sI.attInput,!0)}async translateList(){let t=document.querySelector(this.sI.messageListElSelector);if(t){const e=t.querySelectorAll(this.sI.reciveMessageElSelector),s=t.querySelectorAll(this.sI.sendMessageElSelector),n=()=>{};e.forEach(i=>{this.translater.translateMessage(i,{callback:n,type:"in"})}),s.forEach(i=>{this.translater.translateMessage(i,{callback:n,type:"out"})})}}scrollToBottom(){let t=document.querySelector(this.sI.scrollElSelector);t?t.scroll({top:t.scrollHeight}):console.log("未找到可滚动的元素",this.sI.scrollContent)}clearInput(t){let e=document.querySelector(t);if(!e)return;let s={};this.isMac?s={key:"a",metaKey:!0,bubbles:!0,cancelable:!0}:s={key:"a",ctrlKey:!0,bubbles:!0,cancelable:!0};const n=new KeyboardEvent("keydown",s),i=new KeyboardEvent("keydown",{key:"Backspace",bubbles:!0,cancelable:!0});e.dispatchEvent(n),e.dispatchEvent(i)}setEditable(t,e){try{let s=document.querySelector(t);if(!s)return;e?(s.removeAttribute("contenteditable"),s.setAttribute("contenteditable","true")):(s.removeAttribute("contenteditable"),s.setAttribute("contenteditable","false"))}catch(s){console.warn("setEditable",s)}}setMaskBtn(t,e){if(!document.querySelector("#"+t)){const s=document.createElement("div");s.id=t;const n=document.querySelector(e);n&&(n.style.position="relative",s.style.position="absolute",s.style.right="0",s.style.top="0",s.style.width="50px",s.style.height="100%",s.style.zIndex="999",n.appendChild(s),s.addEventListener("click",async i=>{i.stopPropagation(),t==="mask-btn"?this.sendMessage():t==="att-mask-btn"&&this.attSendMessage()}))}}sendMessageEvent(t){if(t.isAtt)return new Promise(e=>{let s=document.querySelector(t.input);if(!s){console.log("input nil");return}s.focus();const n=new InputEvent("input",{bubbles:!0,cancelable:!0,data:t.text,inputType:"insertText"});s.dispatchEvent(n),s.keydown=null,s.onkeydown=null,t.is_send&&setTimeout(()=>{document.querySelector(t.btn)?document.querySelector(t.btn)?.click():document.querySelector(this.sI.sendBtnBusiness)?.click(),this.setEditable(this.sI.input,!0),this.setEditable(this.sI.attInput,!0),e()},50)});if(this.Store){if(this.setEditable(this.sI.input,!0),!t.is_send)return this.tools.setTextContent(t.text),Promise.resolve();const s=this.getActiveChat()?.id.user,n=this.Store.Chat._models.find(i=>i.__x_id.user===s);return window.require("WAWebSendTextMsgChatAction").sendTextMsgToChat(n,t.text,window.babelHelpers.extends({},{quotedMsg:null,mentionedJidList:[],groupMentions:[],quotedMsgAdminGroupJid:null},{botMsgBodyType:null})),Promise.resolve()}}}exports.WhatsappHandler=S;
//# sourceMappingURL=whatsapp.js.map
