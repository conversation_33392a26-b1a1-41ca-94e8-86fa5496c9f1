"use strict";(function(){try{var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};r.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new r.Error().stack;e&&(r._sentryDebugIds=r._sentryDebugIds||{},r._sentryDebugIds[e]="a33eec8a-7d57-4024-9214-84b945186185",r._sentryDebugIdIdentifier="sentry-dbid-a33eec8a-7d57-4024-9214-84b945186185")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const n=require("electron"),m=require("../tanslate/index.js"),g=require("../utils/fingerprint.js"),c=require("../utils/performance.js"),y=require("dayjs"),w=require("dayjs/plugin/isBetween"),b=require("../tanslate/translateContrast.js");y.extend(w);class v{constructor(e){this.platform=e,this.performanceMonitor=c.createPerformanceMonitor(`Platform-${e}`),this.domUpdater=new c.BatchDOMUpdater}platform=null;mutationObserver=null;pathObserver=null;viewSessionId=null;sI=null;isAutoAiReplying=!1;aiReplyConfig=null;fingerprintSpoofer=null;performanceMonitor=null;domUpdater=null;createTranslateContrast(){this.translateContrast=new b,console.log(this.translateContrast)}roundView(e){if(!e){setTimeout(()=>{this.roundView(document.querySelector("#app"))},200);return}document.documentElement.style.setProperty("background","transparent","important"),document.body.style.setProperty("background","transparent","important");let t={"border-radius":"10px",overflow:"hidden"};Object.keys(t).forEach(a=>{e.style.setProperty(a,t[a],"important")})}async updateAiReplyConfig(){n.ipcRenderer.on("updateAiReplyConfig",(e,t)=>{this.aiReplyConfig=t}),this.aiReplyConfig=await n.ipcRenderer.invoke("getAiReplyConfig")}initFingerprinting(){this.fingerprintSpoofer||(this.fingerprintSpoofer=g.createDefaultSpoofer(),this.fingerprintSpoofer.init(),console.log(`[${this.platform}] Fingerprint spoofing initialized`))}initContextMenu(){let e=document.createElement("div"),t="";const a=[{class:"menu-item",id:"copy-option",textContent:"📋 复制",tag:"div",onClick:async i=>{if(i.preventDefault(),t)try{await navigator.clipboard.writeText(t),i.target.innerText="📋 已复制",setTimeout(()=>{e.style.display="none",i.target.innerText="📋 复制",window.getSelection().removeAllRanges()},500)}catch(o){console.error("❌ 复制失败: "+o.message)}}}],l=[];a.forEach(i=>{const o=document.createElement(i.tag);o.id=i.id,o.className=i.class,o.textContent=i.textContent,o.onclick=i.onClick,l.push(o)}),e.id="custom-menu",e.append(...l),document.body.append(e),document.addEventListener("mouseup",i=>{const o=window.getSelection();if(!o&&!o.focusNode)return;const u=document.querySelector(this.sI.input||this.sI.inputElSelector);u&&u.contains(o.focusNode)||setTimeout(()=>{const p=o.toString().trim();if(p){const s={x:i.clientX,y:i.clientY},f=s.x+110>document.body.clientWidth?s.x-110:s.x,h=s.y+110>document.body.clientWidth?s.y-110:s.y;e.style.top=`${h}px`,e.style.left=`${f}px`,e.style.display="block",t=p}},0)});let d=!1;e.addEventListener("mousedown",()=>{d=!0}),document.addEventListener("mousedown",i=>{e.contains(i.target)||(d=!1)}),document.addEventListener("click",i=>{!d&&!e.contains(i.target)&&(e.style.display="none")})}async getSelectorInfo(){this.sI=await n.ipcRenderer.invoke("getSelectorInfo",{platform:this.platform}),console.log(`SelectorInfo: %O
`,this.sI)}async init(e){if(this.performanceMonitor.mark("platform-init-start"),!this.platform){console.warn("[Platform] platform is null"),alert("无法识别的平台，请联系管理员");return}try{const[t,a]=await Promise.all([this.getViewSessionId(),this.getSelectorInfo()]);this.performanceMonitor.mark("async-init-complete"),this.initFingerprinting(),e instanceof m.Translater&&(this.translater=e,this.translater.init(this)),document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{this.initDOMRelatedFeatures(),this.createTranslateContrast()}):this.initDOMRelatedFeatures(),this.platform==="whatsapp"&&this.roundView(document.querySelector("#app")),this.updateAiReplyConfig().catch(l=>{console.error("[Platform] Failed to update AI reply config:",l)}),this.performanceMonitor.mark("platform-init-end"),console.log(`[${this.platform}] Platform initialized successfully`)}catch(t){throw console.error(`[${this.platform}] Platform initialization failed:`,t),t}}async initDOMRelatedFeatures(){this.performanceMonitor.mark("dom-init-start");try{await this.initViewFunc(),this.initObserver(),this.initContextMenu(),this.initEventHandler(),this.performanceMonitor.mark("dom-init-end"),console.log(`[${this.platform}] DOM features initialized`)}catch(e){console.error(`[${this.platform}] DOM initialization failed:`,e)}}async getViewSessionId(){this.viewSessionId=await n.ipcRenderer.invoke("getViewSessionId"),console.log(`viewSessionId: %s
`,this.viewSessionId)}initObserver(){this.performanceMonitor.mark("observer-init-start"),this.mutationObserver=new c.OptimizedMutationObserver(this._o.bind(this),{throttleDelay:200,batchSize:10}),this.mutationObserver.observe(document.body,{subtree:!0,childList:!0,attributes:!1,characterData:!1}),this.performanceMonitor.mark("observer-init-end"),console.log(`[${this.platform}] Optimized observer initialized`)}_o(e,t){}_u(){}sendMessageToInput(...e){console.log("sendMessageToInput",...e)}autoReplyHandler(){console.log("autoReplyHandler")}async clickAt(e){return await n.ipcRenderer.invoke("clickAt",e)}async aiReply(e){try{return new Promise(async t=>{!this.isAutoAiReplying&&this.aiReplyConfig.ai&&(this.isAutoAiReplying=!0,await e(),console.log("aiReply"),this.isAutoAiReplying=!1,t())})}catch(t){this.isAutoAiReplying=!1,console.log(t)}}translateList(){}async getAiReply(e){try{return await n.ipcRenderer.invoke("aiAutoReply",e)}catch{return}}onUserLoginOut(){n.ipcRenderer.on("onUserLoginOut ",()=>{localStorage.clear(),sessionStorage.clear(),cookieStore.getAll().then(e=>{e.forEach(t=>{cookieStore.delete(t)})})})}async initViewFunc(){return this.onUserLoginOut(),await n.ipcRenderer.invoke("initViewFunc")?(window.addEventListener("hashchange",()=>{this._u(window.location)}),window.addEventListener("popstate",()=>{this._u(window.location)}),window.addEventListener("pushstate",()=>{this._u(window.location),console.log("使用 pushState 改变了 URL:",window.location.href)}),window.addEventListener("replacestate",()=>{this._u(window.location),console.log("使用 replaceState 改变了 URL:",window.location.href)}),!0):!1}sendPlatformUserInfo(e){try{n.ipcRenderer&&typeof n.ipcRenderer.send=="function"&&e.userId&&e.userId!=="0"&&n.ipcRenderer.send("sendUserInfoViewToMain",e)}catch(t){console.error("Failed to send platform user info:",t)}}_sendUnReadCount(e){try{n.ipcRenderer&&typeof n.ipcRenderer.send=="function"&&n.ipcRenderer.send("setUnReadCount",{platform:this.platform,unReadCount:e})}catch(t){console.error("Failed to send unread count:",t)}}sendNewFansList(e){n.ipcRenderer.send("addNewFansList",e)}sendUnReadCount=c.throttle(this._sendUnReadCount,2e3,{trailing:!0});sendCurrentSessionFansInfo(e){n.ipcRenderer.send("sendActiveFansIdViewToMain",e)}initEventHandler(){n.ipcRenderer.on("onMessageMainToView",(e,...t)=>{this.sendMessageToInput(...t)})}onVisibilityChange(){document.addEventListener("visibilitychange",()=>{document.visibilityState==="visible"&&this.translateList()})}}const R=new Map([["www.facebook.com","facebook"],["whatsapp","whatsapp"],["telegram","telegram"],["instagram","instagram"],["tiktok","tiktok"],["business.facebook.com","facebookBusiness"],["x.com","twitter"],["discord.com","discord"]]),S=()=>{let r=JSON.parse('["x.com", "www.facebook.com", "zalo", "telegram", "whatsapp", "instagram", "tiktok", "viber", "business.facebook.com", "discord.com"]').find(e=>!!window.location.host.match(e));return R.get(r)};exports.Platform=v;exports.getPlatform=S;
//# sourceMappingURL=platfrom.js.map
