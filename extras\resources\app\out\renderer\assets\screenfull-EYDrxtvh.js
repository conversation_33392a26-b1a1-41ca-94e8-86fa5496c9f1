import{aa as g}from"./index-BO8ZgokY.js";(function(){try{var n=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};n.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var n=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new n.Error().stack;e&&(n._sentryDebugIds=n._sentryDebugIds||{},n._sentryDebugIds[e]="b8d34fd2-538d-4e2b-8acc-8a0acafce65c",n._sentryDebugIdIdentifier="sentry-dbid-b8d34fd2-538d-4e2b-8acc-8a0acafce65c")})()}catch{}var y=Object.defineProperty,S=(n,e,t)=>e in n?y(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,v=(n,e,t)=>(S(n,typeof e!="symbol"?e+"":e,t),t);class q{constructor(){v(this,"table",{}),v(this,"hashTable",{})}insert(e){const t=Symbol(e);return this.table[e]=!0,this.hashTable[t]=e,t}find(e){return this.hashTable[e]}}new q;var P=Object.defineProperty,R=(n,e,t)=>e in n?P(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,E=(n,e,t)=>(R(n,typeof e!="symbol"?e+"":e,t),t);class D{constructor(){E(this,"requestInstances",new Map),E(this,"listeners",[])}emit(e){this.listeners.forEach(t=>t(e))}subscribe(e){return this.listeners.push(e),()=>{const t=this.listeners.indexOf(e);this.listeners.splice(t,1)}}insert(e,t){this.requestInstances.set(e,{...t}),this.emit({key:e,...t})}update(e,t){this.has(e)&&this.requestInstances.set(e,{...this.requestInstances.get(e),...t})}has(e){return this.requestInstances.has(e)}reset(e){if(this.requestInstances.has(e)){const t=this.requestInstances.get(e);this.requestInstances.clear(),this.insert(e,t)}else this.requestInstances.clear()}getAll(){return this.requestInstances}}new D;function w(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}const T=!!(typeof window<"u"&&window.document&&window.document.createElement);function I(){return w()?document.visibilityState!=="hidden":!0}const F=[];if(w()){const n=()=>{if(I())for(let e=0;e<F.length;e++){const t=F[e];t()}};window.addEventListener("visibilitychange",n,!1)}function z(){return w()&&typeof navigator.onLine<"u"?navigator.onLine:!0}const C=[];if(T){const n=()=>{if(!(!I()||!z()))for(let e=0;e<C.length;e++){const t=C[e];t()}};window.addEventListener("visibilitychange",n,!1),window.addEventListener("focus",n,!1)}/*! js-cookie v3.0.5 | MIT */function d(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var o in t)n[o]=t[o]}return n}var L={read:function(n){return n[0]==='"'&&(n=n.slice(1,-1)),n.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(n){return encodeURIComponent(n).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}};function m(n,e){function t(l,c,r){if(!(typeof document>"u")){r=d({},e,r),typeof r.expires=="number"&&(r.expires=new Date(Date.now()+r.expires*864e5)),r.expires&&(r.expires=r.expires.toUTCString()),l=encodeURIComponent(l).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var s="";for(var i in r)r[i]&&(s+="; "+i,r[i]!==!0&&(s+="="+r[i].split(";")[0]));return document.cookie=l+"="+n.write(c,l)+s}}function o(l){if(!(typeof document>"u"||arguments.length&&!l)){for(var c=document.cookie?document.cookie.split("; "):[],r={},s=0;s<c.length;s++){var i=c[s].split("="),f=i.slice(1).join("=");try{var u=decodeURIComponent(i[0]);if(r[u]=n.read(f,u),l===u)break}catch{}}return l?r[l]:r}}return Object.create({set:t,get:o,remove:function(l,c){t(l,"",d({},c,{expires:-1}))},withAttributes:function(l){return m(this.converter,d({},this.attributes,l))},withConverter:function(l){return m(d({},this.converter,l),this.attributes)}},{attributes:{value:Object.freeze(e)},converter:{value:Object.freeze(n)}})}m(L,{path:"/"});var O=Object.defineProperty,U=(n,e,t)=>e in n?O(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t,h=(n,e,t)=>(U(n,typeof e!="symbol"?e+"":e,t),t);const a=class{constructor(n){h(this,"subscriptions",new Map),h(this,"emitEffectCache",new Map),h(this,"isGlobal"),this.isGlobal=!!n?.global,this.clear()}static getInstance(){return a.instance||(a.instance=new a({global:!0})),a.instance}subscribe(n,e){this.subscriptions.has(n)||this.subscriptions.set(n,new Set);const t=this.subscriptions.get(n);return t.add(e),this.isGlobal||this.emitEffect(n),()=>{t.delete(e),t.size===0&&this.subscriptions.delete(n)}}emit(n,...e){if(typeof n=="string"||typeof n=="number"){const t=this.subscriptions.get(n);t?.forEach(o=>{o?.({params:g(e.length===1?e[0]:e),event:n})}),this.isGlobal||this.emitEffectCache.set(n,{params:g(e.length===1?e[0]:e),event:n})}else throw new TypeError("event must be string or number!")}emitEffect(n){if(this.isGlobal)return;const e=this.emitEffectCache.get(n),t=this.subscriptions.get(n);e&&t?.forEach(o=>{o?.({...e})})}removeListener(n,e){if(!e)this.subscriptions.delete(n);else{const t=this.subscriptions.get(n);t?.delete(e),t&&t.size===0&&this.subscriptions.delete(n)}}clear(){this.subscriptions.clear(),this.emitEffectCache.clear()}};let x=a;h(x,"instance",null);x.getInstance();var p={exports:{}};/*!
* screenfull
* v5.2.0 - 2021-11-03
* (c) Sindre Sorhus; MIT License
*/var _;function B(){return _||(_=1,function(n){(function(){var e=typeof window<"u"&&typeof window.document<"u"?window.document:{},t=n.exports,o=function(){for(var r,s=[["requestFullscreen","exitFullscreen","fullscreenElement","fullscreenEnabled","fullscreenchange","fullscreenerror"],["webkitRequestFullscreen","webkitExitFullscreen","webkitFullscreenElement","webkitFullscreenEnabled","webkitfullscreenchange","webkitfullscreenerror"],["webkitRequestFullScreen","webkitCancelFullScreen","webkitCurrentFullScreenElement","webkitCancelFullScreen","webkitfullscreenchange","webkitfullscreenerror"],["mozRequestFullScreen","mozCancelFullScreen","mozFullScreenElement","mozFullScreenEnabled","mozfullscreenchange","mozfullscreenerror"],["msRequestFullscreen","msExitFullscreen","msFullscreenElement","msFullscreenEnabled","MSFullscreenChange","MSFullscreenError"]],i=0,f=s.length,u={};i<f;i++)if(r=s[i],r&&r[1]in e){for(i=0;i<r.length;i++)u[s[0][i]]=r[i];return u}return!1}(),l={change:o.fullscreenchange,error:o.fullscreenerror},c={request:function(r,s){return new Promise(function(i,f){var u=function(){this.off("change",u),i()}.bind(this);this.on("change",u),r=r||e.documentElement;var b=r[o.requestFullscreen](s);b instanceof Promise&&b.then(u).catch(f)}.bind(this))},exit:function(){return new Promise(function(r,s){if(!this.isFullscreen){r();return}var i=function(){this.off("change",i),r()}.bind(this);this.on("change",i);var f=e[o.exitFullscreen]();f instanceof Promise&&f.then(i).catch(s)}.bind(this))},toggle:function(r,s){return this.isFullscreen?this.exit():this.request(r,s)},onchange:function(r){this.on("change",r)},onerror:function(r){this.on("error",r)},on:function(r,s){var i=l[r];i&&e.addEventListener(i,s,!1)},off:function(r,s){var i=l[r];i&&e.removeEventListener(i,s,!1)},raw:o};if(!o){t?n.exports={isEnabled:!1}:window.screenfull={isEnabled:!1};return}Object.defineProperties(c,{isFullscreen:{get:function(){return!!e[o.fullscreenElement]}},element:{enumerable:!0,get:function(){return e[o.fullscreenElement]}},isEnabled:{enumerable:!0,get:function(){return!!e[o.fullscreenEnabled]}}}),t?n.exports=c:window.screenfull=c})()}(p)),p.exports}B();
//# sourceMappingURL=screenfull-EYDrxtvh.js.map
