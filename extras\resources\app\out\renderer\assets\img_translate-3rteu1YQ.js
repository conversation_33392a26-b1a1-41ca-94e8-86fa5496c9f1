import{u as t2,r as s,w as l2,o as a2,m as s2,f as i,h as t,n as C,i as h,A as b,C as y,D as k,j as w,ab as n2,ac as o2,Z as i2,Y as r2,E as f,ad as c2,ae as u2,O as d2,p as n,F as B,B as Z,a4 as j}from"./index-BO8ZgokY.js";import{U as g2}from"./UserSet-Cd2bnVgC.js";/* empty css                                                                */(function(){try{var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};r.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},v=new r.Error().stack;v&&(r._sentryDebugIds=r._sentryDebugIds||{},r._sentryDebugIds[v]="fa1bb259-1c9f-4fca-b140-a7879be730b9",r._sentryDebugIdIdentifier="sentry-dbid-fa1bb259-1c9f-4fca-b140-a7879be730b9")})()}catch{}const m2={class:"sys_base"},f2={class:"translate_img"},v2={class:"tabs"},_2={class:"lang_translage"},p2={class:"select_info"},C2={class:"trans"},h2=["src"],b2={class:"select_info"},w2={key:0,class:"trans_cont"},L2=["src"],y2={key:1,class:"empty"},k2=["src"],U2={key:2,class:"trans_web_img"},V2={class:"img_url"},R2=["src"],S2={class:"input"},M2={key:3,class:"trans_cont"},x2={class:"trnas_success"},D2={class:"suc_img"},E2=["src"],I2={class:"cent_img"},T2=["src"],A2={class:"suc_img"},B2=["src"],N2={__name:"img_translate",setup(r){const v=t2();d2();const D=s2(),H=new URL("data:image/svg+xml,%3csvg%20width='37'%20height='38'%20viewBox='0%200%2037%2038'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M20.2596%2017.5554C20.4313%2017.5554%2020.5988%2017.6086%2020.739%2017.7076L29.6298%2023.988C30.0047%2024.2529%2030.0939%2024.7714%2029.8291%2025.1463C29.7781%2025.2185%2029.7159%2025.2821%2029.6448%2025.3347L20.7541%2031.9172C20.3852%2032.1903%2019.8647%2032.1127%2019.5916%2031.7438C19.4857%2031.6007%2019.4285%2031.4273%2019.4285%2031.2493V27.0147H12.16C6.98003%2027.0442%204.16723%2024.4842%203.72168%2019.3349C4.17179%2019.8061%206.13904%2022.5868%2012.16%2022.5868C16.174%2022.5868%2018.5969%2022.5407%2019.4285%2022.4485V18.3864C19.4285%2017.9274%2019.8006%2017.5554%2020.2596%2017.5554ZM14.6984%204.81069C14.8043%204.95379%2014.8615%205.12714%2014.8615%205.3052V9.53975H22.13C27.31%209.5103%2030.1228%2012.0703%2030.5684%2017.2196C30.1183%2016.7484%2028.151%2013.9677%2022.13%2013.9677C18.116%2013.9677%2015.6932%2014.0138%2014.8615%2014.106V18.168C14.8615%2018.627%2014.4895%2018.9991%2014.0305%2018.9991C13.8588%2018.9991%2013.6913%2018.9459%2013.551%2018.8468L4.66019%2012.5665C4.28532%2012.3016%204.1961%2011.7831%204.46092%2011.4082C4.51193%2011.336%204.57414%2011.2724%204.6452%2011.2197L13.536%204.63732C13.9049%204.36419%2014.4253%204.44181%2014.6984%204.81069Z'%20fill='%232072F7'/%3e%3c/svg%3e",import.meta.url).href,F=new URL("data:image/svg+xml,%3csvg%20width='108'%20height='108'%20viewBox='0%200%20108%20108'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M66.6559%20104.625H18.2246C10.7996%20104.625%204.72461%2098.55%204.72461%2091.125V37.125C4.72461%2018.5625%2019.9121%203.375%2038.4746%203.375H66.6559C74.0809%203.375%2080.1559%209.45%2080.1559%2016.875V91.125C80.1559%2098.55%2074.0809%20104.625%2066.6559%20104.625Z'%20fill='%23DDDFE7'/%3e%3cpath%20d='M89.9436%20104.625H34.4248C26.9998%20104.625%2020.9248%2098.55%2020.9248%2091.125V35.4375C20.9248%2028.0125%2026.9998%2021.9375%2034.4248%2021.9375H89.9436C97.3686%2021.9375%20103.444%2028.0125%20103.444%2035.4375V91.125C103.444%2098.55%2097.3686%20104.625%2089.9436%20104.625Z'%20fill='%232072F7'/%3e%3cpath%20d='M33.75%2044.0437C33.75%2045.5727%2034.0512%2047.0868%2034.6363%2048.4995C35.2215%2049.9122%2036.0792%2051.1958%2037.1604%2052.277C38.2416%2053.3582%2039.5252%2054.2159%2040.9379%2054.8011C42.3506%2055.3862%2043.8647%2055.6874%2045.3937%2055.6874C46.9228%2055.6874%2048.4369%2055.3862%2049.8496%2054.8011C51.2623%2054.2159%2052.5459%2053.3582%2053.6271%2052.277C54.7083%2051.1958%2055.566%2049.9122%2056.1512%2048.4995C56.7363%2047.0868%2057.0375%2045.5727%2057.0375%2044.0437C57.0375%2040.9555%2055.8108%2037.9939%2053.6271%2035.8103C51.4435%2033.6266%2048.4819%2032.3999%2045.3937%2032.3999C42.3056%2032.3999%2039.344%2033.6266%2037.1604%2035.8103C34.9767%2037.9939%2033.75%2040.9555%2033.75%2044.0437Z'%20fill='white'/%3e%3cpath%20d='M92.3066%2063.9563C87.2441%2058.8938%2078.9754%2058.8938%2073.9129%2063.9563L33.9191%20103.95L33.2441%20104.625H89.7754C97.2004%20104.625%20103.275%2098.55%20103.275%2091.125V75.0938L92.3066%2063.9563Z'%20fill='%232072F7'/%3e%3cpath%20d='M77.1187%20104.625L48.4312%2075.7688C43.3687%2070.7063%2035.1%2070.7063%2030.0375%2075.7688L21.0938%2084.8813V91.125C21.0938%2098.55%2027.1687%20104.625%2034.5937%20104.625H77.1187Z'%20fill='%2383A2FF'/%3e%3c/svg%3e",import.meta.url).href,N=new URL("data:image/svg+xml,%3csvg%20width='108'%20height='109'%20viewBox='0%200%20108%20109'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20clip-path='url(%23clip0_205_3129)'%3e%3cpath%20d='M54%20109C24.1764%20109%200%2084.5997%200%2054.5C0%2024.4003%2024.1764%200%2054%200C83.8236%200%20108%2024.4003%20108%2054.5C108%2084.5997%2083.8236%20109%2054%20109ZM59.7528%2048.8562L59.5632%2048.6685C59.2008%2048.3032%2058.813%2047.9644%2058.4028%2047.6548L54.6636%2051.4274C55.104%2051.6878%2055.5156%2052.0015%2055.8876%2052.3769L56.0844%2052.5646C57.2346%2053.7277%2057.8807%2055.3038%2057.8807%2056.947C57.8807%2058.5903%2057.2346%2060.1664%2056.0844%2061.3295L45.864%2071.6421C44.7109%2072.8015%2043.1495%2073.4525%2041.5218%2073.4525C39.8941%2073.4525%2038.3327%2072.8015%2037.1796%2071.6421L36.9864%2071.4507C35.8364%2070.2883%2035.1904%2068.7128%2035.1904%2067.0701C35.1904%2065.4275%2035.8364%2063.8519%2036.9864%2062.6895L41.6076%2058.0243C40.8133%2056.0393%2040.4235%2053.913%2040.4616%2051.7726L33.3096%2058.9811C28.8972%2063.4332%2028.8972%2070.7168%2033.3096%2075.1664L33.498%2075.3577C37.9104%2079.8062%2045.1236%2079.8062%2049.5348%2075.3577L59.7528%2065.0415C64.1568%2060.5895%2064.1568%2053.3058%2059.7528%2048.8562ZM74.664%2033.8082L74.472%2033.6168C70.0632%2029.1648%2062.8464%2029.1648%2058.4352%2033.6168L48.2172%2043.9331C43.806%2048.3815%2043.806%2055.6651%2048.2172%2060.1183L48.4068%2060.3061C48.7764%2060.6706%2049.1688%2061.0061%2049.5672%2061.3198L53.3064%2057.5435C52.861%2057.2804%2052.4506%2056.9614%2052.0848%2056.594L51.8952%2056.4063C50.7447%2055.2426%2050.0986%2053.6659%2050.0986%2052.0221C50.0986%2050.3782%2050.7447%2048.8015%2051.8952%2047.6378L62.1156%2037.3252C63.2666%2036.1641%2064.8274%2035.5118%2066.4548%2035.5118C68.0822%2035.5118%2069.643%2036.1641%2070.794%2037.3252L70.9836%2037.5166C72.1339%2038.6796%2072.7799%2040.2557%2072.7799%2041.899C72.7799%2043.5423%2072.1339%2045.1184%2070.9836%2046.2814L66.3684%2050.9466C67.1712%2052.9474%2067.5492%2055.0777%2067.512%2057.202L74.6616%2049.9898C79.0728%2045.5414%2079.0728%2038.2578%2074.664%2033.8094V33.8082Z'%20fill='%232072F7'/%3e%3c/g%3e%3cdefs%3e%3cclipPath%20id='clip0_205_3129'%3e%3crect%20width='108'%20height='109'%20fill='white'/%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e",import.meta.url).href,q=new URL(""+new URL("icon49-CDLpLRwp.svg",import.meta.url).href,import.meta.url).href;new URL(""+new URL("img1-DuF9QSfR.png",import.meta.url).href,import.meta.url).href;const O=s(!0);l2(()=>D.query,(l,e)=>{l.chat=="slide"&&(O.value=l.type=="true")},{immediate:!0});const P=()=>{let l=R.value,e="image.jpg";const d=atob(l.split(",")[1]),m=new Array(d.length);for(let L=0;L<d.length;L++)m[L]=d.charCodeAt(L);const o=new Uint8Array(m),x=new Blob([o],{type:"image/png"}),a=URL.createObjectURL(x),p=document.createElement("a");p.href=a,p.download=e,document.body.appendChild(p),p.click(),document.body.removeChild(p),URL.revokeObjectURL(a)},U=s("https://lhyt.blueglob.com"),$=Math.floor(new Date().getTime()/1e3),z=localStorage.getItem("token")?localStorage.getItem("token"):"",W=s({token:z,timestamp:$}),_=s(),Y=(l,e)=>{l.code==1&&(_.value=l.data.path)},Q=l=>l.type!=="image/jpeg"&&l.type!=="image/png"&&l.type!=="image/jpg"?(f.error("上传图片格式需为jpeg、png或jpg"),!1):l.size/1024/1024>2?(f.error("上传图片大小不能超过 2MB!"),!1):!0,V=s(""),R=s(""),E=s(""),I=l=>{let e="";if(l=="img"&&(e=_.value),l=="url"&&(e=V.value),e==""){f({showClose:!0,message:"请上传或填写要翻译的图片内容",type:"info"});return}if(!c.value.to_lang){f({showClose:!0,message:"请选择翻译后语言",type:"info"});return}let d={type:l=="img"?1:2,path:e,fromLang:c.value.from_lang?c.value.from_lang:"auto",toLang:c.value.to_lang};const m=c2.service({lock:!0,text:"Loading",background:"rgba(0, 0, 0, 0.7)"});u2(d).then(o=>{o.code==1&&o.data.picture?(u.value=!0,R.value=o.data.picture,l=="url"&&(E.value=o.data.origin_picture)):o.code==400010?f({showClose:!0,message:o.msg,type:"info"}):f({showClose:!0,message:"图片翻译失败",type:"info"}),m.close()})},c=s({}),u=s(!1),g=s(1),T=l=>{g.value=l,u.value=!1},G=()=>{u.value=!1},A=s();a2(()=>{A.value=D.query.chat});const S=s(!1),J=l=>{S.value=l},M=s({});function K(){i2({}).then(l=>{l.code==1&&(M.value=l.data)})}K();const X=s({});function e2(){r2({}).then(l=>{l.code==1&&(X.value=l.data)})}return e2(),(l,e)=>{const d=k("el-option"),m=k("el-select"),o=k("el-form"),x=k("el-upload");return n(),i("div",m2,[t("div",{class:C(w(v).showHideWord?"main_cont img_translate_main_cont":"main_cont img_translate_main_cont small_tab")},[t("div",{class:C(S.value?"img_translate_cont open_img_translate_cont":"img_translate_cont")},[t("div",f2,[t("div",v2,[t("div",{class:C(g.value==1?"item on":"item"),onClick:e[0]||(e[0]=a=>T(1))},"本地图片",2),t("div",{class:C(g.value==2?"item on":"item"),onClick:e[1]||(e[1]=a=>T(2))},"线上图片",2)]),h(o,{ref:"form",model:c.value},{default:y(()=>[t("div",_2,[t("div",p2,[h(m,{modelValue:c.value.from_lang,"onUpdate:modelValue":e[2]||(e[2]=a=>c.value.from_lang=a),placeholder:"翻译前语言"},{default:y(()=>[(n(!0),i(B,null,Z(M.value,a=>(n(),j(d,{label:a.lang_name,value:a.lang_code},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),t("div",C2,[t("img",{src:w(H),alt:""},null,8,h2)]),t("div",b2,[h(m,{modelValue:c.value.to_lang,"onUpdate:modelValue":e[3]||(e[3]=a=>c.value.to_lang=a),placeholder:"翻译后语言"},{default:y(()=>[(n(!0),i(B,null,Z(M.value,a=>(n(),j(d,{label:a.lang_name,value:a.lang_code},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])])])]),_:1},8,["model"]),g.value==1&&!u.value?(n(),i("div",w2,[h(x,{class:"upload-demo",drag:"",name:"picture","show-file-list":!1,action:U.value+"/api/translate/uploads",headers:W.value,"on-success":Y,"before-upload":Q},{default:y(()=>[_.value?(n(),i("img",{key:0,src:U.value+_.value,alt:""},null,8,L2)):(n(),i("div",y2,[t("img",{src:w(F),alt:""},null,8,k2),e[7]||(e[7]=t("p",null,"单机或拖动图片到此区域进行上传",-1)),e[8]||(e[8]=t("span",null,"仅支持单个图片上传，严禁上传被禁止的图片",-1))]))]),_:1},8,["action","headers"])])):b("",!0),g.value==1&&!u.value?(n(),i("div",{key:1,class:"btn",onClick:e[4]||(e[4]=a=>I("img"))},"翻译")):b("",!0),g.value==2&&!u.value?(n(),i("div",U2,[t("div",V2,[t("img",{src:w(N),alt:""},null,8,R2),e[9]||(e[9]=t("p",null,"请在下方输入图片链接开始翻译",-1)),t("div",S2,[n2(t("input",{type:"text","onUpdate:modelValue":e[5]||(e[5]=a=>V.value=a)},null,512),[[o2,V.value]])]),e[10]||(e[10]=t("div",{class:"tip"},"支持png、jpeg、jpg、bmp格式，图片大小限制5M内，宽高不超过4000*4000",-1)),t("div",{class:"btn_web btn",onClick:e[6]||(e[6]=a=>I("url"))},"开始翻译")])])):b("",!0),u.value?(n(),i("div",M2,[t("div",x2,[t("div",D2,[t("img",{src:g.value==1?U.value+_.value:E.value,alt:""},null,8,E2)]),t("div",I2,[t("img",{src:w(q),alt:""},null,8,T2)]),t("div",A2,[t("img",{src:R.value,alt:"",onClick:P},null,8,B2)])])])):b("",!0),u.value?(n(),i("div",{key:4,class:"btn",onClick:G},"重新翻译")):b("",!0)])],2),t("div",{class:C(S.value?"soft_set open_soft_set":"soft_set")},[h(g2,{ref:"UserSetRef",onOpenSetPage:J,softName:A.value},null,8,["softName"])],2)],2)])}}};export{N2 as default};
//# sourceMappingURL=img_translate-3rteu1YQ.js.map
