"use strict";(function(){try{var a=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};a.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var a=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new a.Error().stack;e&&(a._sentryDebugIds=a._sentryDebugIds||{},a._sentryDebugIds[e]="882a590f-4265-41ee-8d7c-d6e09763e5a6",a._sentryDebugIdIdentifier="sentry-dbid-882a590f-4265-41ee-8d7c-d6e09763e5a6")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});require("electron");require("idb");require("crypto-js");require("dayjs");const u=require("./platfrom.js");class c extends u.Platform{constructor(e){super(e)}idRegular=/(?<=\/t\/)[\d]{1,}(?=\/?)|$/;userId=void 0;userName=void 0;prefilePicture=void 0;chatUserId=void 0;isBindInputFunction=!1;async init(e){await super.init(e),this.bindInputFunction(),this.getCurrentSessionUserId()}_o(e,s){this.bindInputFunction(),this.translateList(),this.getUnreadSessionUserToFans(),this.getUserInfo()}_u(e){this.getCurrentSessionUserId()}getCurrentSessionUserId(){if(location.pathname.includes("/direct/")){const e=()=>{let s=document.querySelector(this.sI.sessionCurrentUserElSelector),n="";if(s&&(n=s.querySelector(this.sI.sessionUserNickNameElSelector)?.innerText),n&&n!==this.chatUserId){if(n==this.userId)return;this.chatUserId=n;let t={mainAccount:this.userId,fansId:n,nickName:n,platform:this.platform};this.sendCurrentSessionFansInfo(t)}else setTimeout(()=>{window.requestAnimationFrame(e)},500)};window.requestAnimationFrame(e)}}getUnreadSessionUserToFans(){if(location.pathname.includes("/direct/")){const s=document.querySelector(this.sI.sessionListElSelector)?.querySelectorAll(this.sI.unreadElSelector);s&&(console.log("sendUnreadCount"),this.sendUnReadCount(s.length));const n=[];s?.forEach(t=>{if(t.hasAttribute("aira-isread"))return;t.setAttribute("aira-isread","true");let i=s.querySelector(this.sI.sessionUserNickNameElSelector)?.innerText;n.push({id:i,nickName:i})}),n.length>0&&this.sendNewFansList({viewSessionId:this.viewSessionId,platform:this.platform,mainAccount:this.userId,unreadListInfo:n})}}clearInput(){let e={};navigator.userAgentData?.platform==="macOS"||/Mac/.test(navigator.userAgent)?e={key:"a",metaKey:!0,bubbles:!0,cancelable:!0}:e={key:"a",ctrlKey:!0,bubbles:!0,cancelable:!0};const s=new KeyboardEvent("keydown",e),n=new KeyboardEvent("keydown",{key:"Backspace",bubbles:!0,cancelable:!0});let t=document.querySelector(this.sI.inputElSelector);t.dispatchEvent(s),t.dispatchEvent(n)}bindInputFunction(){if(location.pathname.includes("/direct/t/")||location.pathname.includes("/direct/e2ee/t/")){const e=s=>{const n=document.querySelector(this.sI.inputElSelector);n?(n.addEventListener("keydown",async t=>{if(n.setAttribute("aria-bind","true"),(t.key==="Enter"||t.keyCode===13)&&!t.shiftKey){t.stopPropagation(),t.stopImmediatePropagation(),this.changeInputEditStatus(!1);let i=this.translater.config;const l=i&&i.trans_over,r=t.target.innerText;if(this.clearInput(),l&&r.trim()){let o=await this.translater.translateInput(r);o?await this.inputMessage(o,!0):await this.inputMessage(r,!1)}else await this.inputMessage(r,!0);this.changeInputEditStatus(!0),this.switchShadowState(!1)}},{capture:!0},!0),n.addEventListener("input",t=>{console.log(console.log(t.target.innerText)),t.stopPropagation(),t.stopImmediatePropagation(),this.createMaskDiv()})):setTimeout(()=>{window.requestAnimationFrame(e)},500)};window.requestAnimationFrame(e)}}keyDownFunc=async e=>{if((e.key==="Enter"||e.keyCode===13)&&!e.shiftKey){e.stopPropagation(),this.changeInputEditStatus(!1);let s=this.translater.config;const n=s&&s.trans_over,t=e.target.innerText;if(this.clearInput(),n&&t.trim()){let i=await this.translater.translateInput(t);i?await this.inputMessage(i,!0):await this.inputMessage(t,!1)}else await this.inputMessage(t,!0);this.changeInputEditStatus(!0),this.switchShadowState(!1)}};sendMessageToInput({type:e,message:s}){this.inputMessage(s,e==="send")}inputMessage(e,s=!1){return console.log("value,",e),new Promise(n=>{let t=document.querySelector(this.sI.inputElSelector);t.focus();const i=new InputEvent("input",{bubbles:!0,cancelable:!0,data:e,inputType:"insertText"});t.dispatchEvent(i),t.keydown=null,t.onkeydown=null,s&&setTimeout(()=>{document.querySelector(this.sI.sendButtonElSelector).click(),n()},50)})}switchShadowState(e){const s=document.getElementById("myShadow");s&&(e?s.style.display="block":s.style.display="none")}createMaskDiv(){if(document.querySelector("#myShadow"))this.switchShadowState(!0);else{let e=document.createElement("div");e.id="myShadow",e.style.position="absolute",e.style.width="100%",e.style.height="100%",e.style.top="0px",e.style.left="0px",e.style.zIndex=9999,e.addEventListener("click",async n=>{n.stopPropagation(),this.changeInputEditStatus(!1);let t=this.translater.config;const i=t&&t.trans_over,r=document.querySelector(this.sI.inputElSelector).innerText;if(this.clearInput(),i&&r.trim()){let o=await this.translater.translateInput(r);o?await this.inputMessage(o,!0):await this.inputMessage(r,!1)}else await this.inputMessage(r,!0);this.changeInputEditStatus(!0),this.switchShadowState(!1)});const s=document.querySelector(this.sI.sendButtonElSelector);s&&(s.style.position="relative",s.appendChild(e))}}changeInputEditStatus(e){let s=document.querySelector(this.sI.inputElSelector);e?(s.removeAttribute("contenteditable"),s.setAttribute("contenteditable","true")):(s.removeAttribute("contenteditable"),s.setAttribute("contenteditable","false"))}async getUserInfo(){if(window.location.pathname.includes("/direct/t/")){const e=document.querySelector(this.sI.userElSelector);let s=!1;if(e){if(this.userName!==e.innerText)s=!0,this.userName=e.innerText,this.userId=e.innerText;else return;const n=e.querySelector("image"),t=n?n.href.baseVal:void 0;this.prefilePicture=t||void 0}if(s){let n={userId:this.userId,platform:this.platform,phone:this.userId,nickName:this.userName||this.userId,session_id:this.viewSessionId};this.sendPlatformUserInfo(n)}}}async translateList(){if(location.pathname.includes("/direct/t/")||location.pathname.includes("/direct/e2ee/t/")){let e=document.querySelector(this.sI.messageListElSelector);e&&(e.querySelectorAll(this.sI.reciveMessageElSelector).forEach((t,i)=>{this.translater.translateMessage(t,{type:"in"})}),e.querySelectorAll(this.sI.sendMessageElSelector).forEach((t,i)=>{this.translater.translateMessage(t,{type:"out"})}))}}}exports.InstagramHandler=c;
//# sourceMappingURL=instagram.js.map
