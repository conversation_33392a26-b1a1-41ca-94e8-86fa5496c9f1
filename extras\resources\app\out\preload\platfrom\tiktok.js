"use strict";(function(){try{var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};r.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var r=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new r.Error().stack;e&&(r._sentryDebugIds=r._sentryDebugIds||{},r._sentryDebugIds[e]="4b9ea6fd-95f3-462e-94ba-e65ad5af97f9",r._sentryDebugIdIdentifier="sentry-dbid-4b9ea6fd-95f3-462e-94ba-e65ad5af97f9")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});require("electron");require("idb");require("crypto-js");require("dayjs");const c=require("./platfrom.js");class d extends c.Platform{constructor(e){super(e)}getUserId(){if(!this.userId){let e=document.querySelector(this.sI.userIdElSelector);if(e){let t=/(?<=\/@).*(?=\?)|(?<=\/@).*/.exec(e.href)[0];if(t)this.userId=t;else return;let s={userId:this.userId,platform:this.platform,phone:this.userId,nickName:this.userId,session_id:this.viewSessionId};this.sendPlatformUserInfo(s)}}}userId;chatUserId;async init(e){await super.init(e),this.getUserId(),this.bindInputFunction()}_o(e,t){this.bindInputFunction(),this.translateList(),this.getUnreadSessionUserToFans(),this.getUserId(),this.getCurrentSessionUserId()}_u(e){this.getCurrentSessionUserId()}getCurrentSessionUserId(){if(location.pathname==="/messages"){let e=0;const t=()=>{let s=document.querySelector(this.sI.currentSessionElSelector);if(s&&e<5){let i=s.querySelector(this.sI.sessionUserNameElSelector);if(i&&this.chatUserId!==i.innerText)this.chatUserId=i.innerText;else return;let n={mainAccount:this.userId,fansId:this.chatUserId,nickName:this.chatUserId,platform:this.platform};this.sendCurrentSessionFansInfo(n)}else setTimeout(()=>{e++,window.requestAnimationFrame(t)},500)};window.requestAnimationFrame(t)}}getUnreadSessionUserToFans(){if(location.pathname==="/messages"){const e=document.querySelectorAll(this.sI.sessionElSelector);let t=0;const s=[];e.forEach(i=>{const n=i.querySelector(this.sI.unreadElSelector);if(n){if(t+=1,n.hasAttribute("aira-isread"))return;{n.setAttribute("aira-isread","true");let a=i.querySelector(this.sI.sessionUserNameElSelector)?.innerText;s.push({id:a,nickName:a})}}}),t>0&&this.sendUnReadCount(t),s.length>0&&this.sendNewFansList({viewSessionId:this.viewSessionId,platform:this.platform,mainAccount:this.userId,unreadListInfo:s})}}bindInputFunction(){if(location.pathname==="/messages"){const e=t=>{const s=document.querySelector(this.sI.inputElSelector);s?(s.addEventListener("keydown",async i=>{if(s.setAttribute("aria-bind","true"),(i.key==="Enter"||i.keyCode===13)&&!i.shiftKey){console.log(i),i.stopPropagation(),i.stopImmediatePropagation(),this.changeInputEditStatus(!1,this.sI.inputElSelector);let n=this.translater.config;const a=n&&n.trans_over,l=i.target.innerText;if(a&&l.trim()){let o=await this.translater.translateInput(l);o?await this.inputMessage(o,!0):await this.inputMessage(l,!1)}else await this.inputMessage(l,!0);this.switchShadowState(!1)}},{capture:!0},!0),s.addEventListener("input",i=>{this.createMaskDiv()})):setTimeout(()=>{window.requestAnimationFrame(e)},500)};window.requestAnimationFrame(e)}}sendMessageToInput({type:e,message:t}){this.inputMessage(t,e==="send")}inputMessage(e,t=!1){return this.changeInputEditStatus(!0,this.sI.inputElSelector),new Promise(s=>{let i=document.querySelector(this.sI.inputElSelector);if(i){let n=i.querySelector(this.sI.inputTextElSelector);if(n)n.innerText=e,i.dispatchEvent(new Event("input",{bubbles:!0,data:e,inputType:"insertText"}));else{const a=new DataTransfer;a.setData("text/plain",e);const l=new ClipboardEvent("paste",{bubbles:!0,cancelable:!0,clipboardData:a});i.dispatchEvent(l)}t&&setTimeout(()=>{document.querySelector(this.sI.sendButtonElSelector)?.dispatchEvent(new MouseEvent("click",{bubbles:!0,view:window})),s()},50)}})}switchShadowState(e){let t=document.querySelector("#myShadow");t&&(e?t.style.display="block":t.style.display="none")}createMaskDiv(){if(document.querySelector("#myShadow"))this.switchShadowState(!0);else{let t=document.createElement("div");t.id="myShadow",t.style.position="absolute",t.style.width="45px",t.style.height="45px",t.style.top="0px",t.style.right="0px",t.style.zIndex=9999;let s=document.querySelector(this.sI.sendButtonContainerElSelector);if(s)s.style.position="relative",s.appendChild(t);else return;t.addEventListener("click",async i=>{i.stopPropagation(),this.changeInputEditStatus(!1,this.sI.inputElSelector);let n=this.translater.config;const a=n&&n.trans_over,o=document.querySelector(this.sI.inputElSelector)?.innerText;if(console.log(o),a&&o.trim()){let u=await this.translater.translateInput(o);u?await this.inputMessage(u,!0):await this.inputMessage(o,!1)}else await this.inputMessage(o,!0);this.switchShadowState(!1)})}}changeInputEditStatus(e,t){try{let s=document.querySelector(t);if(!s)return;e?s.setAttribute("contenteditable","true"):s.setAttribute("contenteditable","false")}catch(s){console.error(s)}}async translateList(){let e=document.querySelector(this.sI.messageListElSelector);e&&(e.querySelectorAll(this.sI.reciveMessageElSelector).forEach((i,n)=>{this.translater.translateMessage(i,{type:"in"})}),e.querySelectorAll(this.sI.sendMessageElSelector).forEach((i,n)=>{this.translater.translateMessage(i,{type:"out"})}))}}exports.TiktokHandler=d;
//# sourceMappingURL=tiktok.js.map
