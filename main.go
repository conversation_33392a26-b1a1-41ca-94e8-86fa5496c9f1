package main

import (
	"embed"
	"os"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
)

//go:embed all:frontend/dist
var assets embed.FS

//go:embed extras/**
var extras embed.FS

const uninstallArg = "--uninstall"

func main() {
	// 创建 App 结构体
	app := NewApp()

	// 判断是否卸载模式
	if len(os.Args) > 1 && os.Args[1] == uninstallArg {
		app.Mode = "uninstall"
	} else {
		app.Mode = "install"
	}

	// 启动 Wails 界面
	err := wails.Run(&options.App{
		Title:  "myproject",
		Width:  512,
		Height: 384,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		Frameless:        true,
		BackgroundColour: &options.RGBA{R: 27, G: 38, B: 54, A: 1},
		OnStartup:        app.startup,
		Bind: []interface{}{
			app,
		},
	})

	if err != nil {
		println("Error:", err.Error())
	}
}
