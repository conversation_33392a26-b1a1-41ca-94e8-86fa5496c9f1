package main

import (
	"embed"
	"fmt"
	"log"
	"os"
	"path/filepath"

	Backend "installer/backend"

	"github.com/wailsapp/wails/v2"
	"github.com/wailsapp/wails/v2/pkg/options"
	"github.com/wailsapp/wails/v2/pkg/options/assetserver"
)

//go:embed all:frontend/dist
var assets embed.FS

//go:embed extras/**
var extras embed.FS

const uninstallArg = "--uninstall"

// 检测运行模式
func detectMode() string {
	// 检查命令行参数
	for _, arg := range os.Args[1:] {
		if arg == uninstallArg {
			return "uninstall"
		}
	}

	// 检查可执行文件名是否包含"uninstall"
	exePath, err := os.Executable()
	if err == nil {
		exeName := filepath.Base(exePath)
		if filepath.Ext(exeName) == ".exe" {
			exeName = exeName[:len(exeName)-4] // 移除.exe扩展名
		}
		// 如果文件名包含uninstall相关字样，则认为是卸载模式
		if len(exeName) > 0 && (exeName == "uninstall" || exeName == "卸载") {
			return "uninstall"
		}
	}

	return "install"
}

func main() {
	// 创建 App 结构体
	app := NewApp()

	// 检测运行模式
	app.Mode = detectMode()

	// 设置ExtrasFS供后端使用
	Backend.ExtrasFS = &extras

	// 记录启动信息
	log.Printf("应用启动，模式: %s", app.Mode)
	if app.Mode == "uninstall" {
		log.Printf("卸载模式启动，参数: %v", os.Args)
	}

	// 设置窗口标题和大小
	title := "应用安装程序"
	width := 600
	height := 500

	if app.Mode == "uninstall" {
		title = "应用卸载程序"
		width = 700
		height = 600
	}

	// 启动 Wails 界面
	err := wails.Run(&options.App{
		Title:  title,
		Width:  width,
		Height: height,
		AssetServer: &assetserver.Options{
			Assets: assets,
		},
		Frameless:        false, // 卸载时显示标题栏，方便用户操作
		BackgroundColour: &options.RGBA{R: 102, G: 126, B: 234, A: 1},
		OnStartup:        app.startup,
		Bind: []interface{}{
			app,
		},
	})

	if err != nil {
		log.Printf("启动应用失败: %v", err)
		fmt.Printf("Error: %s\n", err.Error())
	}
}
