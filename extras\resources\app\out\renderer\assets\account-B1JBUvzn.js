import{U as u0}from"./UserSet-Cd2bnVgC.js";import{r as h,w as Q,T as f0,U as p0,_ as m0,u as h0,t as C0,o as g0,m as _0,W as w0,f as M,h as i,i as o,n as Z,j as r,y as W,C as d,D as U,X as v0,F as H,Y as y0,Z as x0,I as b0,$ as V0,E as v,R as P,a0 as B0,a1 as S0,a2 as k0,O as L0,a3 as M0,p as w,a4 as x,A as I,z as b,a5 as S,a6 as U0,a7 as O,B as T,a8 as G,a9 as Y}from"./index-BO8ZgokY.js";import"./screenfull-EYDrxtvh.js";/* empty css                                                                */(function(){try{var l=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};l.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var l=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},n=new l.Error().stack;n&&(l._sentryDebugIds=l._sentryDebugIds||{},l._sentryDebugIds[n]="7b7c34bf-ba84-41ab-9870-2dda1ccaf242",l._sentryDebugIdIdentifier="sentry-dbid-7b7c34bf-ba84-41ab-9870-2dda1ccaf242")})()}catch{}const p=[];for(let l=0;l<256;++l)p.push((l+256).toString(16).slice(1));function H0(l,n=0){return(p[l[n+0]]+p[l[n+1]]+p[l[n+2]]+p[l[n+3]]+"-"+p[l[n+4]]+p[l[n+5]]+"-"+p[l[n+6]]+p[l[n+7]]+"-"+p[l[n+8]]+p[l[n+9]]+"-"+p[l[n+10]]+p[l[n+11]]+p[l[n+12]]+p[l[n+13]]+p[l[n+14]]+p[l[n+15]]).toLowerCase()}let $;const I0=new Uint8Array(16);function R0(){if(!$){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");$=crypto.getRandomValues.bind(crypto)}return $(I0)}const F0=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),X={randomUUID:F0};function J(l,n,V){if(X.randomUUID&&!l)return X.randomUUID();l=l||{};const f=l.random??l.rng?.()??R0();if(f.length<16)throw new Error("Random bytes length must be >= 16");return f[6]=f[6]&15|64,f[8]=f[8]&63|128,H0(f)}function K(l,n){const V=h(n||{wait:1e3}),f=h(),k=()=>{const{wait:u=1e3,...C}=V.value;return p0(l,u,C)};return f.value=k(),Q(()=>({...V.value}),(u,C)=>{var c;(u.wait!==C?.wait||u.maxWait!==C?.maxWait)&&((c=f.value)==null||c.cancel(),f.value=k())},{deep:!0}),f0(()=>{var u;(u=f.value)==null||u.cancel()}),{run:(...u)=>{var C;return(C=f.value)==null?void 0:C.call(f,...u)},cancel:()=>{var u;(u=f.value)==null||u.cancel()},flush:()=>{var u;(u=f.value)==null||u.flush()},updateOptions:u=>{V.value=u}}}const E0={class:"sys_base"},A0={class:"chatTit"},D0={class:"tit"},Z0={class:"btns"},O0=["src"],T0={class:"items"},G0={class:"clock"},$0=["src"],z0={class:"chatList"},N0={style:{padding:"40px 0 30px"}},q0=["src"],j0={class:"btn-group"},W0={__name:"account",setup(l){const n=h(!1),V=h0();L0();const f=_0();new URL("data:image/svg+xml,%3csvg%20width='96'%20height='96'%20viewBox='0%200%2096%2096'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20filter='url(%23filter0_d_205_1455)'%3e%3ccircle%20cx='48'%20cy='40'%20r='32'%20fill='white'/%3e%3c/g%3e%3cpath%20d='M61.0847%2025.7947C60.6233%2025.5969%2060.1289%2025.498%2059.6345%2025.498H36.3655C35.8381%2025.498%2035.3438%2025.5969%2034.8823%2025.7617C33.3003%2026.3879%2032.1797%2027.8711%2032.1797%2029.6509V50.3821C32.1797%2052.1619%2033.3003%2053.645%2034.9153%2054.2383C35.3767%2054.4031%2035.8711%2054.502%2036.3984%2054.502H59.6345C61.9417%2054.502%2063.8203%2052.6233%2063.8203%2050.3162V29.6509C63.8203%2027.8711%2062.6997%2026.3879%2061.0847%2025.7947ZM44.8359%2034.8254C44.8359%2036.5393%2043.4187%2037.9236%2041.6719%2037.9236C39.925%2037.9236%2038.5078%2036.5393%2038.5078%2034.8254C38.5078%2033.1116%2039.925%2031.7273%2041.6719%2031.7273C43.4187%2031.7273%2044.8359%2033.1116%2044.8359%2034.8254ZM59.6345%2052.3926H36.3655C35.3767%2052.3926%2034.5198%2051.7004%2034.322%2050.7446C34.2891%2050.6128%2034.2891%2050.481%2034.2891%2050.3491V48.7671L40.5183%2043.4937L42.7595%2045.636C43.9131%2046.7566%2045.7588%2046.8225%2047.0112%2045.8008L57.5251%2037.0996C59.5356%2039.2749%2060.9199%2040.7251%2061.7769%2041.4832V50.3162C61.7109%2051.4697%2060.7881%2052.3926%2059.6345%2052.3926Z'%20fill='%23FFA2C0'/%3e%3cdefs%3e%3cfilter%20id='filter0_d_205_1455'%20x='0'%20y='0'%20width='96'%20height='96'%20filterUnits='userSpaceOnUse'%20color-interpolation-filters='sRGB'%3e%3cfeFlood%20flood-opacity='0'%20result='BackgroundImageFix'/%3e%3cfeColorMatrix%20in='SourceAlpha'%20type='matrix'%20values='0%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%20127%200'%20result='hardAlpha'/%3e%3cfeOffset%20dy='8'/%3e%3cfeGaussianBlur%20stdDeviation='8'/%3e%3cfeComposite%20in2='hardAlpha'%20operator='out'/%3e%3cfeColorMatrix%20type='matrix'%20values='0%200%200%200%200.560784%200%200%200%200%200.584314%200%200%200%200%200.698039%200%200%200%200.15%200'/%3e%3cfeBlend%20mode='normal'%20in2='BackgroundImageFix'%20result='effect1_dropShadow_205_1455'/%3e%3cfeBlend%20mode='normal'%20in='SourceGraphic'%20in2='effect1_dropShadow_205_1455'%20result='shape'/%3e%3c/filter%3e%3c/defs%3e%3c/svg%3e",import.meta.url).href,new URL("data:image/svg+xml,%3csvg%20width='96'%20height='96'%20viewBox='0%200%2096%2096'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20filter='url(%23filter0_d_205_1464)'%3e%3ccircle%20cx='48'%20cy='40'%20r='32'%20fill='white'/%3e%3c/g%3e%3cpath%20d='M39%2043H38.5605L34.2804%2047.2804C34.1755%2047.3853%2034.0419%2047.4568%2033.8964%2047.4857C33.7508%2047.5147%2033.6%2047.4998%2033.463%2047.443C33.3259%2047.3862%2033.2088%2047.2901%2033.1264%2047.1667C33.0439%2047.0434%2033%2046.8984%2033%2046.75V43C32.2046%2042.9991%2031.4421%2042.6828%2030.8796%2042.1204C30.3172%2041.5579%2030.0009%2040.7954%2030%2040V28C30.0009%2027.2046%2030.3172%2026.4421%2030.8796%2025.8796C31.4421%2025.3172%2032.2046%2025.0009%2033%2025H52.5C53.2954%2025.0009%2054.0579%2025.3172%2054.6204%2025.8796C55.1828%2026.4421%2055.4991%2027.2046%2055.5%2028V31H43.5C41.0187%2031%2039%2033.0187%2039%2035.5V43ZM63%2032.5H43.5C42.7046%2032.5009%2041.9421%2032.8172%2041.3796%2033.3796C40.8172%2033.9421%2040.5009%2034.7046%2040.5%2035.5V47.5C40.5009%2048.2954%2040.8172%2049.0579%2041.3796%2049.6204C41.9421%2050.1828%2042.7046%2050.4991%2043.5%2050.5H57.4395L61.7196%2054.7804C61.8245%2054.8853%2061.9581%2054.9568%2062.1036%2054.9857C62.2491%2055.0147%2062.4%2054.9998%2062.537%2054.943C62.6741%2054.8862%2062.7912%2054.7901%2062.8736%2054.6667C62.9561%2054.5434%2063%2054.3984%2063%2054.25V50.5C63.7954%2050.4991%2064.5579%2050.1828%2065.1204%2049.6204C65.6828%2049.0579%2065.9991%2048.2954%2066%2047.5V35.5C65.9991%2034.7046%2065.6828%2033.9421%2065.1204%2033.3796C64.5579%2032.8172%2063.7954%2032.5009%2063%2032.5Z'%20fill='%235577FF'/%3e%3cdefs%3e%3cfilter%20id='filter0_d_205_1464'%20x='0'%20y='0'%20width='96'%20height='96'%20filterUnits='userSpaceOnUse'%20color-interpolation-filters='sRGB'%3e%3cfeFlood%20flood-opacity='0'%20result='BackgroundImageFix'/%3e%3cfeColorMatrix%20in='SourceAlpha'%20type='matrix'%20values='0%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%20127%200'%20result='hardAlpha'/%3e%3cfeOffset%20dy='8'/%3e%3cfeGaussianBlur%20stdDeviation='8'/%3e%3cfeComposite%20in2='hardAlpha'%20operator='out'/%3e%3cfeColorMatrix%20type='matrix'%20values='0%200%200%200%200.560784%200%200%200%200%200.584314%200%200%200%200%200.698039%200%200%200%200.15%200'/%3e%3cfeBlend%20mode='normal'%20in2='BackgroundImageFix'%20result='effect1_dropShadow_205_1464'/%3e%3cfeBlend%20mode='normal'%20in='SourceGraphic'%20in2='effect1_dropShadow_205_1464'%20result='shape'/%3e%3c/filter%3e%3c/defs%3e%3c/svg%3e",import.meta.url).href,new URL("data:image/svg+xml,%3csvg%20width='96'%20height='96'%20viewBox='0%200%2096%2096'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20filter='url(%23filter0_d_205_1473)'%3e%3ccircle%20cx='48'%20cy='40'%20r='32'%20fill='white'/%3e%3c/g%3e%3cpath%20d='M50.6212%2027.5566C57.0419%2029.0114%2061.8351%2034.7474%2061.8351%2041.6C61.8351%2049.5531%2055.3798%2056%2047.4175%2056C39.4553%2056%2033%2049.5531%2033%2041.6C33%2034.7474%2037.7932%2029.0114%2044.2139%2027.5566V27.2823C44.2139%2026.4118%2044.5514%2025.5769%2045.1522%2024.9614C45.753%2024.3458%2046.5679%2024%2047.4175%2024C48.2672%2024%2049.0821%2024.3458%2049.6829%2024.9614C50.2837%2025.5769%2050.6212%2026.4118%2050.6212%2027.2823V27.5566ZM47.4175%2032L45.8157%2042.88L47.4175%2045.6L49.0194%2042.88L47.4175%2032ZM61.1312%2033.9291C60.2371%2032.2873%2059.047%2030.8341%2057.6241%2029.6469C58.183%2029.2154%2058.8822%2029.0201%2059.5775%2029.1013C60.2727%2029.1824%2060.9111%2029.5339%2061.3609%2030.0831C61.8107%2030.6323%2062.0376%2031.3374%2061.9949%2032.0531C61.9522%2032.7687%2061.643%2033.4402%2061.1312%2033.9291Z'%20fill='%23FFCE73'/%3e%3cdefs%3e%3cfilter%20id='filter0_d_205_1473'%20x='0'%20y='0'%20width='96'%20height='96'%20filterUnits='userSpaceOnUse'%20color-interpolation-filters='sRGB'%3e%3cfeFlood%20flood-opacity='0'%20result='BackgroundImageFix'/%3e%3cfeColorMatrix%20in='SourceAlpha'%20type='matrix'%20values='0%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%20127%200'%20result='hardAlpha'/%3e%3cfeOffset%20dy='8'/%3e%3cfeGaussianBlur%20stdDeviation='8'/%3e%3cfeComposite%20in2='hardAlpha'%20operator='out'/%3e%3cfeColorMatrix%20type='matrix'%20values='0%200%200%200%200.560784%200%200%200%200%200.584314%200%200%200%200%200.698039%200%200%200%200.15%200'/%3e%3cfeBlend%20mode='normal'%20in2='BackgroundImageFix'%20result='effect1_dropShadow_205_1473'/%3e%3cfeBlend%20mode='normal'%20in='SourceGraphic'%20in2='effect1_dropShadow_205_1473'%20result='shape'/%3e%3c/filter%3e%3c/defs%3e%3c/svg%3e",import.meta.url).href;const k=new URL("data:image/svg+xml,%3csvg%20width='20'%20height='20'%20viewBox='0%200%2020%2020'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M10%201.25C12.4163%201.25%2014.375%203.2375%2014.375%205.69125V7.5H15.7288C16.3625%207.5%2016.875%208.06%2016.875%208.75V17.5C16.875%2018.19%2016.3625%2018.75%2015.7288%2018.75H4.27125C3.6375%2018.75%203.125%2018.19%203.125%2017.5V8.75C3.125%208.06%203.6375%207.5%204.27125%207.5H5.625V5.69125C5.625%203.2375%207.58375%201.25%2010%201.25ZM15.625%208.75H4.375V17.5H15.625V8.75ZM8.75%2013.125V10.625H11.25V13.125H10.625V15.625H9.375V13.125H8.75ZM10%202.5C8.27875%202.5%206.875%203.925%206.875%205.69125V7.5H13.125V5.69125C13.125%203.925%2011.7212%202.5%2010%202.5Z'%20fill='%238F95B2'/%3e%3c/svg%3e",import.meta.url).href;new URL("data:image/svg+xml,%3csvg%20width='20'%20height='21'%20viewBox='0%200%2020%2021'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M15.9001%2013C14.7601%2013%2013.7601%2013.5599%2013.1401%2014.42L9.15007%2012.44C9.70007%2011.67%2010.0201%2010.73%2010.0201%209.70995C10.0201%208.86995%209.80007%208.08995%209.42007%207.40995L14.2201%205.45995C14.7101%206.29995%2015.6201%206.85995%2016.6601%206.85995C18.2201%206.85995%2019.4901%205.58995%2019.4901%204.02995C19.4901%202.46995%2018.2201%201.19995%2016.6701%201.19995C15.1201%201.19995%2013.8401%202.46995%2013.8401%204.02995C13.8401%204.11995%2013.8401%204.21995%2013.8501%204.30995L8.68007%206.39995C7.82007%205.51995%206.62007%204.96995%205.30007%204.96995C2.69007%204.96995%200.570068%207.08995%200.570068%209.69995C0.570068%2012.31%202.69007%2014.43%205.30007%2014.43C6.44007%2014.43%207.49007%2014.0199%208.31007%2013.35L12.6301%2015.5C12.5501%2015.79%2012.5101%2016.09%2012.5101%2016.4C12.5101%2018.27%2014.0401%2019.7999%2015.9101%2019.7999C17.7801%2019.7999%2019.3101%2018.27%2019.3101%2016.4C19.3101%2014.53%2017.7701%2013%2015.9001%2013ZM16.6701%202.39995C17.5701%202.39995%2018.3001%203.12995%2018.3001%204.02995C18.3001%204.92995%2017.5701%205.65995%2016.6701%205.65995C15.7701%205.65995%2015.0401%204.92995%2015.0401%204.02995C15.0401%203.12995%2015.7701%202.39995%2016.6701%202.39995ZM1.77007%209.69995C1.77007%207.74995%203.35007%206.16995%205.30007%206.16995C7.25007%206.16995%208.83007%207.74995%208.83007%209.69995C8.83007%2011.65%207.25007%2013.23%205.30007%2013.23C3.35007%2013.23%201.77007%2011.65%201.77007%209.69995ZM15.9001%2018.6C14.6901%2018.6%2013.7001%2017.61%2013.7001%2016.4C13.7001%2015.19%2014.6901%2014.2%2015.9001%2014.2C17.1101%2014.2%2018.1001%2015.19%2018.1001%2016.4C18.1001%2017.61%2017.1101%2018.6%2015.9001%2018.6Z'%20fill='%23333333'/%3e%3c/svg%3e",import.meta.url).href;const u=new URL("data:image/svg+xml,%3csvg%20width='20'%20height='21'%20viewBox='0%200%2020%2021'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M9.76775%2017.8619C9.90494%2017.9991%2010.091%2018.0762%2010.285%2018.0762C10.3816%2018.0762%2010.4772%2018.057%2010.5664%2018.0199C10.6556%2017.9828%2010.7365%2017.9283%2010.8045%2017.8597C10.8725%2017.7912%2010.9263%2017.7098%2010.9627%2017.6203C10.9991%2017.5309%2011.0174%2017.4351%2011.0166%2017.3385V11.5227H16.8385C17.0325%2011.5227%2017.2186%2011.4456%2017.3558%2011.3084C17.493%2011.1712%2017.5701%2010.9852%2017.5701%2010.7911C17.5701%2010.5971%2017.493%2010.411%2017.3558%2010.2739C17.2186%2010.1367%2017.0325%2010.0596%2016.8385%2010.0596H11.0166V4.23155C11.0166%204.03753%2010.9395%203.85146%2010.8023%203.71427C10.6651%203.57707%2010.4791%203.5%2010.285%203.5C10.091%203.5%209.90494%203.57707%209.76775%203.71427C9.63056%203.85146%209.55348%204.03753%209.55348%204.23155V10.0596H3.73155C3.53753%2010.0596%203.35146%2010.1367%203.21427%2010.2739C3.07707%2010.411%203%2010.5971%203%2010.7911C3%2010.9852%203.07707%2011.1712%203.21427%2011.3084C3.35146%2011.4456%203.53753%2011.5227%203.73155%2011.5227H9.55348V17.3446C9.55348%2017.5386%209.63056%2017.7247%209.76775%2017.8619Z'%20fill='black'/%3e%3c/svg%3e",import.meta.url).href,C=new URL("data:image/svg+xml,%3csvg%20width='186'%20height='154'%20viewBox='0%200%20186%20154'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20d='M91.5329%20141.41C130.487%20141.41%20162.066%20109.754%20162.066%2070.705C162.066%2031.6557%20130.487%200%2091.5329%200C52.5787%200%2021%2031.6557%2021%2070.705C21%20109.754%2052.5787%20141.41%2091.5329%20141.41Z'%20fill='%23EAEEF9'/%3e%3cpath%20d='M39.591%2024.8965H139.927C143.9%2024.8965%20147.022%2028.0263%20147.022%2032.0097V95.6015C147.022%2099.5849%20143.9%20102.715%20139.927%20102.715H39.591C35.6173%20102.715%2032.4951%2099.5849%2032.4951%2095.6015V32.0097C32.4951%2028.0263%2035.7592%2024.8965%2039.591%2024.8965Z'%20fill='%23DAE2EB'/%3e%3cpath%20d='M149.035%2065.6973V96.9952C149.035%20100.979%20145.771%20104.393%20141.655%20104.393H38.4812C34.5075%20104.393%2031.1016%20101.121%2031.1016%2096.9952V62.9944'%20stroke='%232072F7'%20stroke-width='3'%20stroke-linecap='round'%20stroke-linejoin='round'/%3e%3cg%20filter='url(%23filter0_d_205_2765)'%3e%3cpath%20d='M140.135%2048.1655H107.688C104.53%2048.1655%20101.482%2049.255%2099.0861%2051.2155L90.5931%2058.186C88.1976%2060.1465%2085.149%2061.2354%2081.9913%2061.2354H45.0794C41.3773%2061.2354%2038.3286%2064.2852%2038.3286%2067.9884C38.3286%2068.3151%2038.3287%2068.6418%2038.4376%2068.9685L45.1884%20104.911C45.7328%20108.287%2048.5637%20110.792%2051.9392%20110.792H130.554C133.929%20110.792%20136.76%20108.396%20137.304%20105.02L146.777%2055.8986C147.431%2052.1955%20144.926%2048.819%20141.224%2048.1655C140.898%2048.1655%20140.462%2048.1655%20140.135%2048.1655Z'%20fill='white'/%3e%3c/g%3e%3cg%20filter='url(%23filter1_d_205_2765)'%3e%3cpath%20d='M154.853%2038.5247H112.561C108.446%2038.5247%20104.472%2039.9477%20101.35%2042.5085L90.2805%2051.6132C87.1583%2054.1739%2083.1848%2055.5963%2079.0692%2055.5963H30.959C26.1338%2055.5963%2022.1602%2059.5799%2022.1602%2064.4169C22.1602%2064.8437%2022.1603%2065.2703%2022.3022%2065.6971L31.101%20112.644C31.8106%20117.054%2035.5004%20120.326%2039.8998%20120.326H142.364C146.764%20120.326%20150.453%20117.197%20151.163%20112.787L163.51%2048.6255C164.361%2043.7885%20161.097%2039.3782%20156.272%2038.5247C155.846%2038.5247%20155.279%2038.5247%20154.853%2038.5247Z'%20fill='white'/%3e%3c/g%3e%3cpath%20d='M72.9656%2087.1787C75.4737%2087.1787%2077.5069%2085.1402%2077.5069%2082.626C77.5069%2080.1118%2075.4737%2078.074%2072.9656%2078.074C70.4575%2078.074%2068.4243%2080.1118%2068.4243%2082.626C68.4243%2085.1402%2070.4575%2087.1787%2072.9656%2087.1787Z'%20fill='%23081735'/%3e%3cpath%20d='M108.729%2087.0369C111.237%2087.0369%20113.271%2084.9991%20113.271%2082.4848C113.271%2079.9706%20111.237%2077.9321%20108.729%2077.9321C106.221%2077.9321%20104.188%2079.9706%20104.188%2082.4848C104.188%2084.9991%20106.221%2087.0369%20108.729%2087.0369Z'%20fill='%23081735'/%3e%3cpath%20d='M95.2229%2092.0444H86.5659V94.1784H95.2229V92.0444Z'%20fill='%23081735'/%3e%3cdefs%3e%3cfilter%20id='filter0_d_205_2765'%20x='28.3286'%20y='29.1655'%20width='128.553'%20height='82.6265'%20filterUnits='userSpaceOnUse'%20color-interpolation-filters='sRGB'%3e%3cfeFlood%20flood-opacity='0'%20result='BackgroundImageFix'/%3e%3cfeColorMatrix%20in='SourceAlpha'%20type='matrix'%20values='0%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%20127%200'%20result='hardAlpha'/%3e%3cfeOffset%20dy='-9'/%3e%3cfeGaussianBlur%20stdDeviation='5'/%3e%3cfeColorMatrix%20type='matrix'%20values='0%200%200%200%200.397708%200%200%200%200%200.47749%200%200%200%200%200.575%200%200%200%200.13%200'/%3e%3cfeBlend%20mode='normal'%20in2='BackgroundImageFix'%20result='effect1_dropShadow_205_2765'/%3e%3cfeBlend%20mode='normal'%20in='SourceGraphic'%20in2='effect1_dropShadow_205_2765'%20result='shape'/%3e%3c/filter%3e%3cfilter%20id='filter1_d_205_2765'%20x='0.160156'%20y='27.5247'%20width='185.485'%20height='125.802'%20filterUnits='userSpaceOnUse'%20color-interpolation-filters='sRGB'%3e%3cfeFlood%20flood-opacity='0'%20result='BackgroundImageFix'/%3e%3cfeColorMatrix%20in='SourceAlpha'%20type='matrix'%20values='0%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%200%20127%200'%20result='hardAlpha'/%3e%3cfeOffset%20dy='11'/%3e%3cfeGaussianBlur%20stdDeviation='11'/%3e%3cfeColorMatrix%20type='matrix'%20values='0%200%200%200%200.397708%200%200%200%200%200.47749%200%200%200%200%200.575%200%200%200%200.18%200'/%3e%3cfeBlend%20mode='normal'%20in2='BackgroundImageFix'%20result='effect1_dropShadow_205_2765'/%3e%3cfeBlend%20mode='normal'%20in='SourceGraphic'%20in2='effect1_dropShadow_205_2765'%20result='shape'/%3e%3c/filter%3e%3c/defs%3e%3c/svg%3e",import.meta.url).href,c=C0({engineCode:"googlehw",recive_lang:"zh",send_lang:"en",self:!0,trans_over:!0}),e0=()=>{c.engineCode="googlehw",c.recive_lang="zh",c.send_lang="en",c.self=!0,c.trans_over=!0},z=h({});function t0(){y0({}).then(t=>{t.code==1&&(z.value=t.data)})}t0();const y=h();g0(()=>{y.value=f.query.platform,B()});const l0=h(!0);Q(()=>f.query,(t,e)=>{t.platform&&(y.value=t.platform,B()),t.chat=="slide"&&(l0.value=t.type=="true")});const R=h(!1),a0=t=>{R.value=t},N=K(async()=>{V0({}).then(t=>{t.code==1&&t.data.status==0?v({showClose:!0,message:"会话已达上限",type:"error"}):(y.value,`${J()}${parseInt(new Date().getTime()/1e3)}`,y.value,n.value=!0)})},100).run,F=h({});function o0(){x0({}).then(t=>{t.code==1&&(F.value=t.data)})}o0();const n0=K(async()=>{const t=`persist:${J()}${parseInt(new Date().getTime()/1e3)}`;await r0(t),window.electron.ipcRenderer.send("setTranslateConfig",{config:P(c),sessionId:t}),n.value=!1},300).run,E=h({});function s0(){b0({}).then(t=>{t.code==1&&(E.value=t.data)})}s0();const i0=async()=>{let t=L.value,e=[];for(let m=0;m<t.length;m++)e.push(t[m].session_id);await A(e,1);let s=[];const g=h(new Map([["facebook","https://www.facebook.com"],["instagram","https://www.instagram.com/direct/t/lanhai/"],["telegram","https://web.telegram.org/k"],["whatsapp","https://web.whatsapp.com"],["zalo","https://chat.zalo.me/"],["tiktok","https://www.tiktok.com/messages"],["facebookBusiness","https://business.facebook.com/latest/inbox/all/"],["twitter","https://x.com/messages"],["discord","https://discord.com/channels/@me"]]));t.forEach(m=>{let _="";m.platform==="facebookbusienss"?_="facebookBusiness":_=m.platform,s.push({name:m.session_id,url:g.value.get(_),platform:_,itemInfo:P(m),noAttach:!0})}),window.electron.ipcRenderer.send("batchStartView",{batchStartViewList:s})},A=async(t,e)=>(e==2&&window.electron.ipcRenderer.send("deleteView",t.map(s=>`${s.platform}/${s.id}`).join(",")),k0({sessionId:t,status:e}).then(s=>{s.code==1?(v({showClose:!0,message:s.msg,type:"success"}),B(),window.electron.ipcRenderer.send("updateOpenChatList")):v({showClose:!0,message:s.msg,type:"info"})})),r0=t=>{let e={platformId:y.value,sessionId:t};return M0(e).then(s=>{s.code==1?(v({showClose:!0,message:"创建会话成功",type:"success"}),window.electron.ipcRenderer.send("updateOpenChatList"),B()):v({showClose:!0,message:s.msg,type:"info"})})},D=h([]),B=()=>{let t={platformId:y.value};w0(t).then(e=>{e.code==1?D.value=e.data:D.value=[]})},q=()=>{let t=L.value,e=[];for(let g=0;g<t.length;g++)e.push(t[g].session_id);let s={sessionIds:e};if(e.length==0){v({showClose:!0,message:"请选择要删除的账号",type:"info"});return}B0.confirm("确认要删除当前账号吗?","删除提醒",{distinguishCancelAndClose:!0,confirmButtonText:"确定",cancelButtonText:"取消"}).then(()=>{S0(s).then(g=>{g.code==1?(window.electron.ipcRenderer.send("deleteView",t.map(m=>`${m.platform}/${m.id}`).join(",")),v({showClose:!0,message:"删除会话成功",type:"success"}),window.electron.ipcRenderer.send("updateOpenChatList"),B()):v({showClose:!0,message:g.msg,type:"info"})})}).catch(()=>{})},d0=t=>{L.value=[t],q()},L=h([]),c0=t=>{L.value=t};return(t,e)=>{const s=U("el-table-column"),g=U("el-text"),m=U("el-table"),_=U("el-form-item");return w(),M(H,null,[i("div",E0,[i("div",{class:Z(r(V).showHideWord?"main_cont account_main_cont":"main_cont account_main_cont small_tab")},[i("div",{class:Z(R.value?"account_cont open_account_cont":"account_cont")},[i("div",A0,[i("div",D0,[e[8]||(e[8]=i("h2",null,"会话列表",-1)),i("span",null,"占用/总数: "+W(E.value.used_ports)+"/"+W(E.value.ports),1)]),i("div",Z0,[i("div",{class:"item",onClick:e[0]||(e[0]=(...a)=>r(N)&&r(N)(...a))},[i("img",{src:r(u),alt:""},null,8,O0),e[9]||(e[9]=i("p",null,"新增会话",-1))]),i("div",T0,[i("div",{class:"item",onClick:e[1]||(e[1]=a=>i0())},"一键启动"),i("div",{class:"item",onClick:q},"批量删除")])]),i("div",G0,[e[10]||(e[10]=i("span",null,"已链接安全加密链路",-1)),i("img",{src:r(k),alt:""},null,8,$0)])]),i("div",z0,[o(m,{"empty-text":"无数据",data:D.value,"tooltip-effect":"dark",height:"100%",style:{width:"100%"},onSelectionChange:c0},{empty:d(()=>[i("div",N0,[i("img",{src:r(C),alt:""},null,8,q0)])]),default:d(()=>[o(s,{type:"selection",width:"55"}),o(s,{label:"序号",type:"index",width:"60"}),o(s,{prop:"create_time",label:"创建时间",width:"160"}),o(s,{prop:"platform",label:"会话记录",width:"100"}),o(s,{prop:"account_name",label:"昵称",width:"120"}),o(s,{prop:"account_code",label:"用户名",width:"120"}),o(s,{prop:"account_mobile",label:"手机号",width:"120"}),o(s,{prop:"name",label:"状态",width:"100"},{default:d(a=>[a.row.status==1?(w(),x(g,{key:0,class:"mx-1",type:"primary"},{default:d(()=>e[11]||(e[11]=[b("开启",-1)])),_:1,__:[11]})):I("",!0),a.row.status==2?(w(),x(g,{key:1,class:"mx-1",type:"danger"},{default:d(()=>e[12]||(e[12]=[b("关闭",-1)])),_:1,__:[12]})):I("",!0)]),_:1}),o(s,{prop:"address",label:"地区","show-overflow-tooltip":""}),o(s,{prop:"name",label:"备注信息",width:"180"}),o(s,{prop:"name",label:"操作",width:"150",fixed:"right"},{default:d(a=>[a.row.status==2?(w(),x(r(S),{key:0,class:"setBtn",onClick:j=>A([a.row.session_id],1),type:"text",size:"small"},{default:d(()=>e[13]||(e[13]=[b("启动",-1)])),_:2,__:[13]},1032,["onClick"])):I("",!0),a.row.status==1?(w(),x(r(S),{key:1,class:"setBtn close",onClick:j=>A([a.row.session_id],2),type:"text",size:"small"},{default:d(()=>e[14]||(e[14]=[b("关闭",-1)])),_:2,__:[14]},1032,["onClick"])):I("",!0),o(r(S),{class:"setBtn del",onClick:j=>d0(a.row),type:"text",size:"small"},{default:d(()=>e[15]||(e[15]=[b("删除",-1)])),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"])])],2),i("div",{class:Z(R.value?"soft_set open_soft_set":"soft_set")},[o(u0,{ref:"UserSetRef",onOpenSetPage:a0,softName:y.value},null,8,["softName"])],2)],2)]),o(r(v0),{modelValue:n.value,"onUpdate:modelValue":e[7]||(e[7]=a=>n.value=a),title:"提示-新建会话",width:"600",class:"custom-dialog"},{default:d(()=>[o(r(U0),{model:c,"label-width":"120px",class:"custom-form mt-30px!"},{default:d(()=>[o(_,{label:"Engine Code"},{default:d(()=>[o(r(O),{modelValue:c.engineCode,"onUpdate:modelValue":e[2]||(e[2]=a=>c.engineCode=a),placeholder:"请选择引擎",style:{width:"100%"}},{default:d(()=>[(w(!0),M(H,null,T(z.value,a=>(w(),x(r(G),{key:a.engine_code,label:a.engine_name,value:a.engine_code},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(_,{label:"接收语言"},{default:d(()=>[o(r(O),{modelValue:c.recive_lang,"onUpdate:modelValue":e[3]||(e[3]=a=>c.recive_lang=a),placeholder:"请选择接收语言",style:{width:"100%"}},{default:d(()=>[(w(!0),M(H,null,T(F.value,a=>(w(),x(r(G),{label:a.lang_name,value:a.lang_code},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),o(_,{label:"发送语言"},{default:d(()=>[o(r(O),{modelValue:c.send_lang,"onUpdate:modelValue":e[4]||(e[4]=a=>c.send_lang=a),placeholder:"请选择发送语言",style:{width:"100%"}},{default:d(()=>[(w(!0),M(H,null,T(F.value,a=>(w(),x(r(G),{label:a.lang_name,value:a.lang_code},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),o(_,{label:"自动翻译"},{default:d(()=>[o(r(Y),{modelValue:c.self,"onUpdate:modelValue":e[5]||(e[5]=a=>c.self=a)},null,8,["modelValue"])]),_:1}),o(_,{label:"翻译后发送"},{default:d(()=>[o(r(Y),{modelValue:c.trans_over,"onUpdate:modelValue":e[6]||(e[6]=a=>c.trans_over=a)},null,8,["modelValue"])]),_:1}),o(_,null,{default:d(()=>[i("div",j0,[o(r(S),{type:"primary",onClick:r(n0)},{default:d(()=>e[16]||(e[16]=[b("提交",-1)])),_:1,__:[16]},8,["onClick"]),o(r(S),{onClick:e0},{default:d(()=>e[17]||(e[17]=[b("重置",-1)])),_:1,__:[17]})])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])],64)}}},K0=m0(W0,[["__scopeId","data-v-d1e774c8"]]);export{K0 as default};
//# sourceMappingURL=account-B1JBUvzn.js.map
