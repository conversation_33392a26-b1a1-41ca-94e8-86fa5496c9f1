"use strict";(function(){try{var o=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};o.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var o=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new o.Error().stack;e&&(o._sentryDebugIds=o._sentryDebugIds||{},o._sentryDebugIds[e]="b7247915-1b19-46f2-9c2e-0e5b129e6f6f",o._sentryDebugIdIdentifier="sentry-dbid-b7247915-1b19-46f2-9c2e-0e5b129e6f6f")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});require("electron");require("idb");require("crypto-js");require("dayjs");const d=require("./platfrom.js"),i={userElSelector:"nav>a:last-of-type",inputElSelector:"div.DraftEditor-editorContainer>div",inputTextElSelector:"div.public-DraftStyleDefault-block>span[data-offset-key]>span",sessionUserNameElSelector:'div[class="css-146c3p1 r-dnmrzs r-1udh08x r-1udbk01 r-3s2u2q r-bcqeeo r-1ttztb7 r-qvutc0 r-37j5jr r-a023e6 r-rjixqe r-16dba41 r-18u37iz r-1wvb978"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]',currentSessionUserNameElSelector:'.r-x572qd div[class="css-146c3p1 r-dnmrzs r-1udh08x r-1udbk01 r-3s2u2q r-bcqeeo r-1ttztb7 r-qvutc0 r-37j5jr r-a023e6 r-rjixqe r-16dba41 r-18u37iz r-1wvb978"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]',messageListElSelector:"[class='css-175oi2r r-16y2uox r-10m9thr r-1h0z5md r-f8sm7e r-13qz1uu r-3pj75a r-1ye8kvj']",unreadElSelector:".r-l5o3uw",sessionElSelector:'div[role="tablist"]',sendButtonElSelector:'button[class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1ez5h0i r-2yi16 r-1qi8awa r-1loqt21 r-o7ynqc r-6416eg r-1ny4l3l"]',sendButtonContainerElSelector:'[class="css-175oi2r r-1awozwy r-1sw30gj r-1867qdf r-18u37iz r-l00any r-jusfrs r-tuq35u r-1h0ofqe"]',reciveMessageElSelector:'div[class="css-175oi2r r-1habvwh r-1wbh5a2"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]',sendMessageElSelector:'div[class="css-175oi2r r-obd0qt r-1wbh5a2"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]'};class h extends d.Platform{constructor(e){super(e)}async getUserId(){if(!this.userId){let e=document.querySelector(i.userElSelector);if(e){this.userId=this.userName=e.href.split("https://x.com/").join("");let t={userId:this.userId,platform:this.platform,phone:this.userId,nickName:this.userName,session_id:this.viewSessionId};this.sendPlatformUserInfo(t)}}}userId;chatUserId;userName;async init(e){await super.init(e),this.getUserId(),this.bindInputFunction()}_o(e,t){/\/messages\/?/.test(location.pathname)&&(this.bindInputFunction(),this.translateList(),this.getUnreadSessionUserToFans(),this.getUserId(),this.getCurrentSessionUserId())}_u(e){this.getCurrentSessionUserId()}getCurrentSessionUserId(){let e=0;const t=()=>{let s=document.querySelector(i.currentSessionUserNameElSelector);if(s&&e<5){if(this.chatUserId!==s.innerText){const n=/(?<=@).*/.exec(s.innerText);this.chatUserId=n[0]}else return;let r={mainAccount:this.userId,fansId:this.chatUserId,nickName:this.chatUserId,platform:this.platform};this.sendCurrentSessionFansInfo(r)}else setTimeout(()=>{e++,window.requestAnimationFrame(t)},500)};window.requestAnimationFrame(t)}getUnreadSessionUserToFans(){const e=document.querySelectorAll(i.sessionElSelector);let t=0;const s=[];e.forEach(r=>{const n=r.querySelector(i.unreadElSelector);if(n){if(t+=1,n.hasAttribute("aira-isread"))return;{n.setAttribute("aira-isread","true");let a=/(?<=@).*/.exec(r.querySelector(i.sessionUserNameElSelector)?.innerText)[0];s.push({id:a,nickName:a})}}}),t>0&&this.sendUnReadCount(t),s.length>0&&this.sendNewFansList({viewSessionId:this.viewSessionId,platform:this.platform,mainAccount:this.userId,unreadListInfo:s})}bindInputFunction(){const e=t=>{const s=document.querySelector(i.inputElSelector);s?s.addEventListener("keydown",async r=>{if(this.createMaskDiv(),s.setAttribute("aria-bind","true"),(r.key==="Enter"||r.keyCode===13)&&!r.shiftKey){console.log(r),r.stopPropagation(),r.stopImmediatePropagation(),this.changeInputEditStatus(!1,i.inputElSelector);let n=this.translater.config;const c=n&&n.trans_over,a=r.target.outerText;if(c&&a.trim()){let l=await this.translater.translateInput(a);console.log("tv",l),l?await this.inputMessage(l,!0):await this.inputMessage(a,!1)}else await this.inputMessage(a,!0);this.switchShadowState(!1)}},{capture:!0},!0):setTimeout(()=>{window.requestAnimationFrame(e)},500)};window.requestAnimationFrame(e)}sendMessageToInput({type:e,message:t}){this.inputMessage(t,e==="send")}inputMessage(e,t=!1){return this.changeInputEditStatus(!0,i.inputElSelector),new Promise(s=>{let r=document.querySelector(i.inputElSelector);if(r){r.focus();let n=r.querySelector(i.inputTextElSelector);if(n)n.innerText=e,r.dispatchEvent(new Event("input",{bubbles:!0,data:e,inputType:"insertText"}));else{const c=new DataTransfer;c.setData("text/plain",e);const a=new ClipboardEvent("paste",{bubbles:!0,cancelable:!0,clipboardData:c});r.dispatchEvent(a)}t&&setTimeout(()=>{document.querySelector(i.sendButtonElSelector)?.dispatchEvent(new MouseEvent("click",{bubbles:!0,view:window})),s()},50)}})}switchShadowState(e){let t=document.querySelector("#myShadow");t&&(e?t.style.display="block":t.style.display="none")}createMaskDiv(){if(document.querySelector("#myShadow"))this.switchShadowState(!0);else{let t=document.createElement("div");t.id="myShadow",t.style.position="absolute",t.style.width="68px",t.style.height="40px",t.style.top="0px",t.style.right="0px",t.style.zIndex=9999;let s=document.querySelector(i.sendButtonContainerElSelector);if(s)s.style.position="relative",s.appendChild(t);else return;t.addEventListener("click",async r=>{r.stopPropagation(),this.changeInputEditStatus(!1,i.inputElSelector);let n=this.translater.config;const c=n&&n.trans_over,l=document.querySelector(i.inputElSelector)?.outerText;if(console.log(l),c&&l.trim()){let u=await this.translater.translateInput(l);u?await this.inputMessage(u,!0):await this.inputMessage(l,!1)}else await this.inputMessage(l,!0);this.switchShadowState(!1)})}}changeInputEditStatus(e,t){try{let s=document.querySelector(t);if(!s)return;e?s.setAttribute("contenteditable","true"):s.setAttribute("contenteditable","false")}catch(s){console.error(s)}}async translateList(){let e=document.querySelector(i.messageListElSelector);e&&(e.querySelectorAll(i.reciveMessageElSelector).forEach((r,n)=>{this.translater.translateMessage(r,{type:"in"})}),e.querySelectorAll(i.sendMessageElSelector).forEach((r,n)=>{this.translater.translateMessage(r,{type:"out"})}))}}exports.TwitterHandler=h;
//# sourceMappingURL=twitter.js.map
