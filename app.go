package main

import (
	"context"
	"fmt"
	"sync"
	"time"

	Backend "installer/backend"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// UninstallProgressData 卸载进度数据
type UninstallProgressData struct {
	Step     string `json:"step"`
	Progress int    `json:"progress"`
}

// App struct
type App struct {
	ctx               context.Context
	Mode              string
	uninstallProgress chan UninstallProgressData
	uninstallMutex    sync.Mutex
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{
		uninstallProgress: make(chan UninstallProgressData, 10),
	}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
}

// Greet returns a greeting for the given name
func (a *App) Greet(name string) string {
	return fmt.Sprintf("Hello %s, It's show time!", name)
}

func (a *App) Test() string {
	return "test"
}

// Uninstall 快速卸载（保持兼容性）
func (a *App) Uninstall() bool {
	Backend.UninstallApp()
	return true
}

// Install 安装应用
func (a *App) Install() bool {
	_, err := Backend.InstallApp()
	return err == nil
}

// GetMode 获取当前模式
func (a *App) GetMode() string {
	return a.Mode
}

// StartUninstallWithProgress 启动带进度的卸载
func (a *App) StartUninstallWithProgress() error {
	a.uninstallMutex.Lock()
	defer a.uninstallMutex.Unlock()

	// 创建进度回调函数
	progressCallback := func(step string, progress int) {
		// 发送进度事件到前端
		runtime.EventsEmit(a.ctx, "uninstall-progress", UninstallProgressData{
			Step:     step,
			Progress: progress,
		})
	}

	// 在新的goroutine中执行卸载
	go func() {
		if err := Backend.StartUninstallProcess(progressCallback); err != nil {
			runtime.EventsEmit(a.ctx, "uninstall-error", err.Error())
		}
	}()

	return nil
}

// GetUninstallSteps 获取卸载步骤信息
func (a *App) GetUninstallSteps() []map[string]interface{} {
	steps := []map[string]interface{}{
		{"name": "准备卸载", "description": "正在准备卸载程序...", "progress": 10},
		{"name": "删除快捷方式", "description": "正在删除快捷方式...", "progress": 25},
		{"name": "删除注册表", "description": "正在清理注册表信息...", "progress": 40},
		{"name": "删除文件", "description": "正在删除程序文件...", "progress": 70},
		{"name": "清理临时文件", "description": "正在清理临时文件...", "progress": 85},
		{"name": "完成卸载", "description": "卸载完成", "progress": 100},
	}
	return steps
}

// 模拟卸载进度：每 500ms 返回一次百分比（保持兼容性）
func (a *App) StartUninstall() []int {
	progress := []int{}
	for i := 0; i <= 100; i += 20 {
		progress = append(progress, i)
		time.Sleep(500 * time.Millisecond)
	}
	return progress
}
