package main

import (
	"context"
	"fmt"
	"time"

	Backend "installer/backend"
)

// App struct
type App struct {
	ctx  context.Context
	Mode string
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
}

// Greet returns a greeting for the given name
func (a *App) <PERSON><PERSON>(name string) string {
	return fmt.Sprintf("Hello %s, It's show time!", name)
}

func (a *App) Test() string {
	return "test"
}

func (a *App) Uninstall() bool {
	Backend.UninstallApp()
	return true
}

func (a *App) Install() bool {
	Backend.InstallApp()
	return true
}

// 在前端初始化时可以调用
func (a *App) GetMode() string {
	return a.Mode
}

// 模拟卸载进度：每 500ms 返回一次百分比
func (a *App) StartUninstall() []int {
	progress := []int{}
	for i := 0; i <= 100; i += 20 {
		progress = append(progress, i)
		time.Sleep(500 * time.Millisecond)
	}
	return progress
}
