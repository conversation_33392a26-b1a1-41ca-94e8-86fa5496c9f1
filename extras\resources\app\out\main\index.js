"use strict";
!function() {
  try {
    var e = "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof globalThis ? globalThis : "undefined" != typeof self ? self : {};
    e.SENTRY_RELEASE = { id: "2.1.3" };
  } catch (e2) {
  }
}();
;
{
  try {
    (function() {
      var e = "undefined" != typeof window ? window : "undefined" != typeof global ? global : "undefined" != typeof globalThis ? globalThis : "undefined" != typeof self ? self : {}, n = new e.Error().stack;
      n && (e._sentryDebugIds = e._sentryDebugIds || {}, e._sentryDebugIds[n] = "f5852d45-4f17-472c-b029-d237da4eb2bc", e._sentryDebugIdIdentifier = "sentry-dbid-f5852d45-4f17-472c-b029-d237da4eb2bc");
    })();
  } catch (e) {
  }
}
;
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const electron = require("electron");
const electronUpdater = require("electron-updater");
const utils = require("@electron-toolkit/utils");
const path = require("path");
const http = require("http");
const CryptoJS = require("crypto-js");
const url = require("url");
const axios = require("axios");
const crypto = require("crypto");
const utils_googleTranslateApi = require("./utils/googleTranslateApi.js");
require("worker_threads");
const lowdb = require("lowdb");
const node = require("lowdb/node");
const fs = require("fs/promises");
require("@sentry/electron/main");
const GlobalObject = {
  viewManager: null,
  messageManager: null,
  mainWindow: null,
  viewBounds: {
    x: 0,
    y: 0,
    width: 1,
    height: 1
  },
  selectorInfo: null,
  // 全局翻译配置
  translateConfig: {
    engineCode: "googlehk",
    recive_lang: "zh",
    send_lang: "en",
    self: true,
    trans_over: true,
    self_many: false,
    login_trans_his: false,
    send_trans_zh: false
  },
  translateDB: null
};
const loadingCssString = ".__translateloading {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.dot {\r\n  width: 10px;\r\n  height: 10px;\r\n  margin: 0 8px;\r\n  background-color: currentColor;\r\n  border-radius: 50%;\r\n  animation: bounce 1.2s infinite ease-in-out both;\r\n}\r\n\r\n.dot:nth-child(1) {\r\n  animation-delay: 0s;\r\n}\r\n\r\n.dot:nth-child(2) {\r\n  animation-delay: 0.2s;\r\n}\r\n\r\n.dot:nth-child(3) {\r\n  animation-delay: 0.4s;\r\n}\r\n\r\n@keyframes bounce {\r\n\r\n  0%,\r\n  100% {\r\n    transform: scale(0.3);\r\n  }\r\n\r\n  50% {\r\n    transform: scale(1);\r\n  }\r\n}";
const contextMenuCssString = "#custom-menu {\r\n  position: fixed;\r\n  display: none;\r\n  background-color: white;\r\n  border: 1px solid #ccc;\r\n  padding: 6px 0px;\r\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  z-index: 9999;\r\n  min-width: 100px;\r\n  color: #514848;\r\n}\r\n\r\n#custom-menu .menu-item {\r\n  padding: 6px 12px;\r\n  cursor: pointer;\r\n}\r\n\r\n#custom-menu .menu-item:hover {\r\n  background-color: #c8d6f5;\r\n}";
function getAllMethods(obj) {
  const methods = /* @__PURE__ */ new Set();
  Object.getOwnPropertyNames(obj).filter((name) => typeof obj[name] === "function").forEach((method) => methods.add(method));
  let proto = Object.getPrototypeOf(obj);
  while (proto) {
    Object.getOwnPropertyNames(proto).filter(
      (name) => name !== "constructor" && typeof proto[name] === "function" && [
        "__defineGetter__",
        "__defineSetter__",
        "hasOwnProperty",
        "__lookupGetter__",
        "__lookupSetter__",
        "isPrototypeOf",
        "propertyIsEnumerable",
        "toString",
        "valueOf",
        "toLocaleString"
      ].indexOf(name) === -1
    ).forEach((method) => methods.add(method));
    proto = Object.getPrototypeOf(proto);
  }
  return Array.from(methods);
}
function getUs(isMacOs) {
  let us = "";
  if (isMacOs) {
    us = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36";
  } else {
    us = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0";
  }
  return us;
}
class ViewManager {
  constructor() {
    this.viewsMap = /* @__PURE__ */ new Map();
  }
  viewsMap;
  setViewBounds(view2, { x, y, width, height }) {
    const safeX = Math.max(1, Math.floor(x || 0));
    const safeY = Math.max(1, Math.floor(y || 0));
    const safeWidth = Math.max(1, Math.floor(width || 0));
    const safeHeight = Math.max(1, Math.floor(height || 0));
    view2.setBounds({
      x: safeX,
      y: safeY,
      width: safeWidth,
      height: safeHeight
    });
  }
  addView(viewConfig) {
    try {
      let view2;
      if (!this.viewsMap.has(viewConfig.name)) {
        const s = electron.session.fromPartition(`${viewConfig.itemInfo.session_id}`, {
          cache: true
        });
        console.log("s.storagePath", s.storagePath);
        s.webRequest.onBeforeSendHeaders((details, callback) => {
          callback({
            requestHeaders: {
              ...details.requestHeaders,
              "User-Agent": getUs(utils.platform.isMacOS)
            }
          });
        });
        s.setPermissionRequestHandler((webContents, permission, callback) => {
          callback(true);
        });
        view2 = new ViewInfo({
          ...viewConfig,
          webPreferences: {
            sandbox: false,
            nodeIntegration: false,
            contextIsolation: ["whatsapp", "facebook"].includes(viewConfig.itemInfo.platform) ? false : true,
            media: true,
            transparent: true,
            webSecurity: true,
            preload: preloadJsPathMap.get(viewConfig.platform),
            session: s
          }
        });
        ViewHandler.init(view2);
        if (utils.platform.isMacOS) {
          view2.webContents.once("did-finish-load", () => {
            this.viewsMap.set(viewConfig.name, view2);
            if (!viewConfig.noAttach) {
              this.attachViewToWindow(view2);
            }
          });
        } else if (utils.platform.isWindows) {
          this.viewsMap.set(viewConfig.name, view2);
          if (!viewConfig.noAttach) {
            this.attachViewToWindow(view2);
          }
        } else {
          this.viewsMap.set(viewConfig.name, view2);
          if (!viewConfig.noAttach) {
            this.attachViewToWindow(view2);
          }
        }
        if (!electron.app.isPackaged || false) {
          view2.webContents.openDevTools();
        }
      } else {
        let view22 = this.viewsMap.get(viewConfig.name);
        GlobalObject.mainWindow.contentView.addChildView(view22);
      }
      view2.webContents.loadURL(viewConfig.url, { userAgent: getUs(utils.platform.isMacOS) }).catch((err) => {
        console.log(err, "loadurl error");
        return err;
      });
      return true;
    } catch (error) {
      console.log(error);
      return false;
    }
  }
  attachViewToWindow(view2) {
    this.setViewBounds(view2, GlobalObject.viewBounds);
    GlobalObject.mainWindow.contentView.addChildView(view2);
  }
  getView(name) {
    return this.viewsMap.get(name);
  }
  deleteView(name) {
    return this.viewsMap.delete(name);
  }
}
class ViewInfo extends electron.WebContentsView {
  constructor(viewConfig) {
    super(viewConfig);
    this.name = viewConfig.name;
    this.url = viewConfig.url;
    this.itemInfo = viewConfig.itemInfo;
  }
}
function onMainBwReisze(bw) {
  bw.on("resize", () => {
    if (GlobalObject.viewManager?.viewsMap) {
      GlobalObject.viewManager.viewsMap.forEach((view2) => {
        if (view2 && GlobalObject.viewBounds) {
          GlobalObject.viewManager.setViewBounds(view2, GlobalObject.viewBounds);
        }
      });
    }
  });
}
class ViewHandler {
  static init(view2) {
    this.setUserAgent(view2);
    this.setViewOpenHandler(view2);
    this.insertCss(view2);
  }
  static interceptUrlRegExpMap = /* @__PURE__ */ new Map([
    ["facebook", [new RegExp("https://business.facebook.com/")]]
  ]);
  static setViewOpenHandler(view2) {
    const handler = (e) => {
      console.log(e);
      this.interceptUrlRegExpMap.forEach((v, k) => {
        if (k === view2.itemInfo.platform) {
          v.forEach((r) => {
            if (r.test(e.url)) {
              e.preventDefault();
              electron.dialog.showMessageBox({
                title: "提示",
                message: `当前url已被拦截:
 ${r.toString().split("\\").join("")}`
              });
            }
          });
        }
      });
    };
    view2.webContents.on("will-navigate", handler);
    view2.webContents.on("new-window", handler);
  }
  static setUserAgent(view2) {
    view2.webContents.setUserAgent(getUs(utils.platform.isMacOS));
  }
  static insertCss(view2) {
    view2.webContents.on("dom-ready", () => {
      view2.webContents.insertCSS(loadingCssString);
      view2.webContents.insertCSS(contextMenuCssString);
      console.log("view load success");
    });
  }
}
const icon = path.join(__dirname, "../../resources/icon.png");
let tray = null;
function createWindow() {
  let w = new electron.BrowserWindow({
    width: 1452,
    height: 800,
    minWidth: 1100,
    minHeight: 660,
    show: false,
    autoHideMenuBar: true,
    frame: false,
    icon,
    transparent: true,
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      contextIsolation: true,
      userAgent: getUs(utils.platform.isMacOS)
      // webviewTag: true
    }
  });
  onMainBwReisze(w);
  electron.session.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
    if (permission === "media") {
      callback(true);
    } else {
      callback(false);
    }
  });
  w.on("ready-to-show", () => {
    if (process.platform === "win32") {
      w.hookWindowMessage(278, () => {
        w.setEnabled(false);
        setTimeout(() => {
          w.setEnabled(true);
        });
        return true;
      });
    }
    w.show();
  });
  w.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return {
      action: "deny"
    };
  });
  if (utils.is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    w.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    console.log(path.join(__dirname, "../renderer/index.html"));
    w.loadFile(path.join(__dirname, "../renderer/index.html"));
  }
  electron.ipcMain.on("getVesion", (event, arg) => {
    let v = electron.app.getVersion();
    event.reply("sendVersion", v);
  });
  electron.ipcMain.handle("onUserLoginOut", async (e, p) => {
    await electron.dialog.showMessageBox(GlobalObject.mainWindow, {
      type: "question",
      buttons: ["确定"],
      defaultId: 0,
      title: "退出提醒",
      message: p
    });
    return true;
  });
  electron.ipcMain.on("sendToMainUserInfo", (_event, ...args) => {
    console.log(GlobalObject.viewManager.viewsMap);
    let viewId = _event.sender.id;
    let session_id;
    GlobalObject.viewManager.viewsMap.forEach((v) => {
      if (v.webContents.id === viewId) {
        session_id = v.itemInfo.session_id;
      }
    });
    w.webContents.send("getUserInfo", ...args, session_id);
  });
  try {
    const iconPath = utils.platform.isMacOS ? path.join(__dirname, "../../build/icon.icns") : path.join(__dirname, "../../icon.png");
    const trayIcon = electron.nativeImage.createFromPath(iconPath);
    if (trayIcon.isEmpty()) {
      console.warn("Tray icon not found, skipping tray creation");
    }
    tray = new electron.Tray(trayIcon.resize({ width: 20, height: 20, quality: "best" }));
    electron.app.on("quit", () => {
      tray.destroy();
    });
    tray.setToolTip("蓝海译通");
    const contextMenu = electron.Menu.buildFromTemplate([
      {
        label: "打开",
        click: () => {
          w.show();
        }
      },
      {
        label: "退出",
        click: () => {
          electron.app.quit();
        }
      }
    ]);
    tray.setContextMenu(contextMenu);
    if (utils.platform.isMacOS) {
      utils.platform.isMacOS;
    } else {
      tray.on("click", () => {
        w.isVisible() ? w.focus() : w.show();
      });
    }
  } catch (error) {
    console.error("Failed to create tray:", error);
  }
  let is_hand_btn = false;
  if (process.platform !== "darwin") {
    electronUpdater.autoUpdater.autoDownload = false;
    let downloadUrl = `${"https://lhyt.blueglob.com"}/update`;
    console.log("downloadUrl:", downloadUrl);
    electronUpdater.autoUpdater.setFeedURL(downloadUrl);
    electronUpdater.autoUpdater.on("checking-for-update", () => {
      console.log("Checking for update...");
    });
    electronUpdater.autoUpdater.on("update-available", (info) => {
      console.log("Update available:");
      console.log(info);
      try {
        w.webContents.send("update-available", info.version);
      } catch (error) {
        console.log(error);
      }
    });
    electronUpdater.autoUpdater.on("update-not-available", (info) => {
      console.log("No update available:", info);
      try {
        w.webContents.send("no-update-version", is_hand_btn);
      } catch (error) {
        console.log(error);
      }
    });
    electronUpdater.autoUpdater.on("error", (err) => {
      console.error("Update error:", err);
      electron.dialog.showMessageBox({
        type: "warning",
        title: "更新出错，请稍后重试",
        message: "是否要关闭窗口？"
      });
    });
    electronUpdater.autoUpdater.on("download-progress", (progressObj) => {
      w.webContents.send(
        "download-progress-number",
        progressObj.percent,
        JSON.stringify(progressObj)
      );
    });
    electronUpdater.autoUpdater.on("update-downloaded", (info) => {
      console.log("Update downloaded:", info);
      electronUpdater.autoUpdater.quitAndInstall();
    });
    electronUpdater.autoUpdater.checkForUpdates().then((info) => {
      if (info.updateInfo) {
        console.log("new version:", info.updateInfo);
      } else {
        console.log("当前已是最新版本");
      }
    }).catch((error) => {
    });
    electron.ipcMain.on("check-for-update", () => {
      console.log("kkkk");
      is_hand_btn = true;
      electronUpdater.autoUpdater.checkForUpdates().then((info) => {
        if (info.updateInfo) {
          console.log("发现新版本:", info.updateInfo);
        } else {
          console.log("当前已是最新版本");
        }
      }).catch((error) => {
        console.error("检查更新失败:", error);
      });
    });
    electron.ipcMain.on("now_down_exe", () => {
      electronUpdater.autoUpdater.downloadUpdate();
    });
    electron.ipcMain.on("quit-and-install", () => {
      electronUpdater.autoUpdater.quitAndInstall();
    });
  }
  electron.ipcMain.on("updateOpenChatList", (event) => {
    w.webContents.send("app-update-chat-list");
  });
  return w;
}
const Config = {
  iscloseApp: false,
  themeMode: "light",
  isShowMessage: false,
  ai: false,
  aiTimeRang: []
};
class MessageManager {
  count = 0;
  messageMap = new Proxy(
    {},
    {
      set: (target, field, value) => {
        target[field] = value;
        this.watchMessage(target);
        return true;
      }
    }
  );
  getCount() {
    return this.count;
  }
  setMessageCount(params) {
    const { platform, unReadCount } = params;
    this.messageMap[platform] = { unReadCount };
  }
  watchMessage(target) {
    let c = Object.values(this.messageMap).filter((item) => item && typeof item.unReadCount === "number").map((item) => item.unReadCount).reduce((pre, cur) => {
      return pre + cur;
    }, 0);
    this.count = c;
    if (Config.isShowMessage) {
      this.showMessageCount();
    } else {
      this.hideMessageCount();
    }
    GlobalObject.mainWindow?.webContents.send("unReadMessageCountChange", target);
  }
  showMessageCount() {
    try {
      if (utils.platform.isMacOS && typeof electron.app.setBadgeCount === "function") {
        electron.app.setBadgeCount(this.count);
      }
    } catch (error) {
      console.error("Failed to show message count:", error);
    }
  }
  hideMessageCount() {
    try {
      if (utils.platform.isMacOS) {
        electron.app.setBadgeCount(0);
      }
    } catch (error) {
      console.error("Failed to hide message count:", error);
    }
  }
}
const baseUrl = "https://lhyt.blueglob.com";
const urlStr = baseUrl + "/api/translate/translate";
url.parse(urlStr);
async function getTranslate(data, token) {
  const postData = JSON.stringify(data);
  const currentTime = /* @__PURE__ */ new Date();
  const timestamp = Math.floor(currentTime.getTime() / 1e3);
  return axios.post(urlStr, data, {
    headers: {
      "Content-Type": "application/json",
      "Content-Length": Buffer.byteLength(postData),
      token,
      timestamp,
      sign: sha256(postData + timestamp + token)
    },
    timeout: 1e4
  }).then((res) => {
    return res.data;
  }).catch(async (error) => {
    if (error.code === "ECONNABORTED" && error.config.url === "/api/translate/translate") {
      let config = error.config;
      try {
        let res = await axios.get(
          `https://translate.google.com/translate_a/single?client=at&sl=${config.fromLang}&tl=${config.toLang}&dt=t&q=${config.text}`
        );
        let data2 = res.data;
        let tranlateText = "";
        data2[0].forEach((item) => {
          tranlateText += item[0];
        });
        return {
          code: 1,
          data: {
            result: tranlateText
          }
        };
      } catch (_err) {
        return error;
      }
    }
    return error;
  });
}
const urlStrSave = baseUrl + "/api/chat/addChatRecord";
const urlObjSave = url.parse(urlStrSave);
async function saveChat(data, token) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      chatData: data
    });
    const currentTime = /* @__PURE__ */ new Date();
    const timestamp = Math.floor(currentTime.getTime() / 1e3);
    const options = {
      hostname: urlObjSave.hostname,
      port: urlObjSave.port || 80,
      path: urlObjSave.pathname,
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Content-Length": Buffer.byteLength(postData),
        token,
        timestamp,
        sign: sha256(postData + timestamp + token)
      }
    };
    const req = http.request(options, (res) => {
      let data2 = "";
      res.setEncoding("utf8");
      res.on("data", (chunk) => {
        data2 += chunk;
      });
      res.on("end", () => {
        try {
          const message = JSON.parse(data2);
          resolve(message);
        } catch (error) {
          reject(error);
        }
      });
    });
    req.on("error", (error) => {
      reject(error);
    });
    req.write(postData);
    req.end();
  });
}
const urlSelectorInfo = baseUrl + "/api/index/getdomlist";
const urlSelectorUrl = url.parse(urlSelectorInfo);
async function getSelectorInfoFunc(data, token) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(data);
    const currentTime = /* @__PURE__ */ new Date();
    const timestamp = Math.floor(currentTime.getTime() / 1e3);
    const options = {
      hostname: urlSelectorUrl.hostname,
      port: urlSelectorUrl.port || 80,
      path: urlSelectorUrl.pathname,
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Content-Length": Buffer.byteLength(postData),
        token,
        timestamp,
        sign: sha256(postData + timestamp + token)
      }
    };
    const req = http.request(options, (res) => {
      let data2 = "";
      res.setEncoding("utf8");
      res.on("data", (chunk) => {
        data2 += chunk;
      });
      res.on("end", () => {
        try {
          const message = JSON.parse(data2);
          resolve(message);
        } catch (error) {
          reject(error);
        }
      });
    });
    req.on("error", (error) => {
      reject(error);
    });
    req.write(postData);
    req.end();
  });
}
const autoReplyAiUrlInfo = baseUrl + "/api/index/platformCssConfig";
new url.URL(autoReplyAiUrlInfo);
function sha256(message) {
  return CryptoJS.SHA256(message).toString();
}
const API_BASE_URL = "https://aikf.lhyq360.com/app/ai";
let RolePrompt;
const getDatasetRolePrompt = async (params) => {
  var config = {
    method: "post",
    url: "http://aikf.lhyq360.com/app/ai/embedding/getDateset",
    headers: {
      "Content-Type": "application/json",
      "Accept": "*/*",
      "Host": "aikf.lhyq360.com",
      "Connection": "keep-alive"
    },
    data: JSON.stringify(params)
  };
  const res = await axios(config);
  RolePrompt = res.data?.data?.rolePrompt;
  return res.data?.data?.rolePrompt;
};
async function searchSystemData({ content, dataset = 1 }) {
  try {
    const { data: response } = await axios.post(`${API_BASE_URL}/embedding/search`, {
      content,
      dataset
    });
    if (response.data && response.code === 0) {
      return response || {};
    } else {
      console.log(response, content, dataset);
      console.log(new Error(response.msg || "查询system数据失败"));
    }
  } catch (error) {
    console.error("搜索system数据时发生错误:", error);
    throw error;
  }
}
async function chatCompletions(params) {
  console.log("params", params);
  try {
    const response = await axios.post(`${API_BASE_URL}/chat/completions`, params);
    if (response.data) {
      return response.data;
    }
  } catch (error) {
    console.error("聊天回复时发生错误:", error);
  }
}
const createNowMicroseconds = () => {
  const ns = process.hrtime.bigint();
  const us = ns / 1000n;
  return Number(us);
};
const getAiReplyApi = async ({ question, dataset, messageList, chat_id }) => {
  console.log("getAiReplyApi", question, dataset, messageList, chat_id);
  const { code, data, msg } = await searchSystemData({ content: question, dataset });
  if (code === 0) {
    return await chatCompletions({
      "temperature": 0.5,
      "stream": false,
      "messages": [
        {
          "role": "system",
          "content": `${RolePrompt}${data.toString()}`
          // "content": data.toString()
        },
        ...messageList.map((v) => {
          return {
            "role": v.role,
            "content": v.content
          };
        })
      ],
      "prompt": question,
      // 用户提问的内容
      "model": "gpt-4o-mini",
      // 固定
      "chat_id": chat_id,
      // 当天聊天室的ID 可使用用户的ID
      "user_message_id": createNowMicroseconds(),
      // 微妙时间戳
      "assistant_message_id": createNowMicroseconds(),
      // 微妙时间戳
      "role_id": dataset,
      // 先固定 41  模拟使用 后续根据请求的 商户 需要请求 获取 此ID
      "max_tokens": 2e3
      // 固定
    });
  } else {
    console.error(msg);
  }
};
class TabViewApi {
  createTabView() {
    electron.ipcMain.handle("createTabView", (_, arg) => {
      GlobalObject.viewManager.addView(arg);
    });
  }
  showView() {
    electron.ipcMain.on("showView", (_, arg) => {
      GlobalObject.viewManager.viewsMap.forEach((view3) => {
        GlobalObject.mainWindow.contentView.removeChildView(view3);
      });
      let view2 = GlobalObject.viewManager.getView(arg.name);
      if (view2) {
        GlobalObject.mainWindow.contentView.addChildView(view2);
        view2.setBounds(GlobalObject.viewBounds);
      } else {
        GlobalObject.viewManager.addView(arg);
      }
    });
  }
  showWindow() {
    electron.ipcMain.on("showWindow", (_) => {
      GlobalObject.viewManager.viewsMap.forEach((view2) => {
        if (Config.ai && (view2.itemInfo.platform === "whatsapp" || view2.itemInfo.platform === "facebook")) {
          view2.setBounds({ x: 0, y: 0, width: 1, height: 1 });
        } else {
          GlobalObject.mainWindow.contentView.removeChildView(view2);
        }
      });
      console.log(GlobalObject.mainWindow.contentView);
    });
  }
  setViewBounds() {
    electron.ipcMain.handle("setViewBounds", (_, arg) => {
      console.log(arg);
      GlobalObject.viewBounds = arg;
      if (GlobalObject.mainWindow?.contentView?.children) {
        GlobalObject.mainWindow.contentView.children.forEach((item) => {
          item.setBounds(GlobalObject.viewBounds);
        });
      }
      return true;
    });
  }
  viewExecJavaScript() {
    electron.ipcMain.handle("viewExecJavaScript", (_, { nowTabbarId: viewName, jsString }) => {
      try {
        return GlobalObject.viewManager.getView(viewName)?.webContents.executeJavaScript(jsString);
      } catch (error) {
        return false;
      }
    });
  }
  getCurrentView() {
    electron.ipcMain.handle("getCurrentView", () => {
      const children = GlobalObject.mainWindow.contentView.children;
      if (children && children.length > 0) {
        let v = children[children.length - 1];
        return v.itemInfo || false;
      }
      return false;
    });
  }
  deleteView() {
    electron.ipcMain.on("deleteView", (_event, itemIds) => {
      if (!itemIds || typeof itemIds !== "string") return;
      itemIds.split(",").forEach((id2) => {
        if (!id2.trim()) return;
        try {
          let view2 = GlobalObject.viewManager.getView(id2);
          if (view2 && view2.itemInfo && view2.itemInfo.session_id) {
            let s = electron.session.fromPartition(`persist:account-${view2.itemInfo.session_id}`);
            GlobalObject.mainWindow.contentView.removeChildView(view2);
            GlobalObject.viewManager.deleteView(id2);
            if (s && typeof s.clearStorageData === "function") {
              s.clearStorageData().catch((err) => {
                console.warn("Failed to clear session data:", err);
              });
            }
          }
        } catch (error) {
          console.error(`Failed to delete view ${id2}:`, error);
        }
      });
    });
  }
  initViewFunc() {
    electron.ipcMain.handle("initViewFunc", (event) => {
      return new Promise((resolve) => {
        let execS = `; (function () {
  spoofNavigator();
  spoofCanvas();
  console.log(navigator);
  function spoofNavigator() {
    const overwrite = (obj, prop, value) => {
      Object.defineProperty(obj, prop, {
        get: () => value,
        configurable: true,
      });
    };
    overwrite(navigator, 'userAgentData', {
      brands: [
        { brand: "'Google Chrome'", version: "137" },
        { brand: "Chromium", version: "137" },
        { brand: "Not/A)Brand", version: "24" },


      ],
      mobile: false,
      platform: "Windows",
      toJSON: function () { return this },
      getHighEntropyValues: (hints) => {
        return Promise.resolve({
          platform: "Windows",
          platformVersion: "10.0.0",
          architecture: "x86",
          model: "",
          uaFullVersion: "*********"
        });
      }
    });
    overwrite(navigator, 'language', "zh-CN");
    overwrite(navigator, 'languages', ['zh-CN', "en", "en-US"]);
    overwrite(navigator, 'hardwareConcurrency', 4);
    overwrite(navigator, 'deviceMemory', 8);
    overwrite(navigator, 'vendor', "Google Inc.");
    overwrite(navigator, 'vendorSub', '');
    overwrite(navigator,"sayswho","Chrome 137");
  }
  function spoofCanvas() {
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function (...args) {
      try {
        const ctx = this.getContext('2d');
        injectCanvasNoise(ctx, this.width, this.height);
      } catch (e) { }
      return originalToDataURL.apply(this, args);
    };

    const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
    CanvasRenderingContext2D.prototype.getImageData = function (...args) {
      injectCanvasNoise(this, this.canvas.width, this.canvas.height);
      return originalGetImageData.apply(this, args);
    };

    function injectCanvasNoise(ctx, width, height) {
      if (!ctx || typeof ctx.fillText !== 'function') return;
      ctx.save();
      ctx.globalAlpha = 0.01;
      ctx.fillStyle = '#000';
      ctx.font = '16px Arial';
      ctx.fillText("canvas-noise-${Math.random() * 10}", 1, height - 1);
      ctx.restore();
    }
  }
  // 重写 history.pushState 方法
  const originalPushState = history.pushState
  history.pushState = function (...args) {
    const result = originalPushState.apply(this, args)
    window.dispatchEvent(new Event('pushstate'))
    return result
  }
  // 重写 history.replaceState 方法
  const originalReplaceState = history.replaceState
  history.replaceState = function (...args) {
    const result = originalReplaceState.apply(this, args)
    window.dispatchEvent(new Event('replacestate'))
    return result
  }
  return true
})();`;
        event.sender.executeJavaScript(execS).then((r) => {
          resolve(r);
        }).catch((err) => {
          console.log(err);
          resolve(false);
        });
      });
    });
  }
  getViewSessionId() {
    electron.ipcMain.handle("getViewSessionId", (_event) => {
      let viewId = _event.sender.id;
      let session_id;
      GlobalObject.viewManager.viewsMap.forEach((v) => {
        if (v.webContents.id === viewId) {
          session_id = v.itemInfo.session_id;
        }
      });
      return session_id;
    });
  }
  getAiReplyConfig() {
    electron.ipcMain.handle("getAiReplyConfig", (_event) => {
      let o = { ai: Config.ai, aiTimeRang: Config.aiTimeRang };
      return o;
    });
  }
  refreshView() {
    electron.ipcMain.on("refreshView", (event, name) => {
      let v = GlobalObject.viewManager.getView(name);
      if (v) {
        v.webContents.reload();
      }
    });
  }
  sendMessageToView() {
    electron.ipcMain.handle("sendMessageWindowToMain", (_event, params) => {
      const children = GlobalObject.mainWindow.contentView.children;
      if (children && children.length > 0) {
        let v = children[children.length - 1];
        v.webContents.send("onMessageMainToView", params);
        return true;
      }
      return false;
    });
  }
  sendUserInfoMainToWindow() {
    electron.ipcMain.on("sendUserInfoViewToMain", (_event, params) => {
      const { userId } = params;
      let viewId = _event.sender.id;
      GlobalObject.viewManager.viewsMap.forEach((v) => {
        if (v.webContents.id === viewId) {
          v.itemInfo.userId = userId;
          GlobalObject.mainWindow.webContents.send("onGetUserInfo", { ...v.itemInfo, ...params });
        }
      });
    });
  }
  sendActiveFansIdMainToWindow() {
    electron.ipcMain.on("sendActiveFansIdViewToMain", (_event, params) => {
      let viewId = _event.sender.id;
      GlobalObject.viewManager.viewsMap.forEach((v) => {
        if (v.webContents.id === viewId) {
          GlobalObject.mainWindow.webContents.send("sendActiveFansId", { ...v.itemInfo, ...params });
        }
      });
    });
  }
  sendNewFansListMainToWindow() {
    electron.ipcMain.on("addNewFansList", (_event, params) => {
      let viewId = _event.sender.id;
      GlobalObject.viewManager.viewsMap.forEach((v) => {
        if (v.webContents.id === viewId) {
          GlobalObject.mainWindow.webContents.send("onAddNewFansList", { ...v.itemInfo, ...params });
        }
      });
    });
  }
  getSelectorInfo() {
    electron.ipcMain.handle("getSelectorInfo", (_event, params) => {
      let { platform } = params;
      return GlobalObject.selectorInfo[platform];
    });
  }
  batchStartView() {
    electron.ipcMain.on("batchStartView", (_event, { batchStartViewList }) => {
      if (!Array.isArray(batchStartViewList)) return;
      batchStartViewList.forEach((item) => {
        if (item && item.name) {
          GlobalObject.viewManager.addView(item);
        }
      });
    });
  }
  clickAt() {
    electron.ipcMain.handle("clickAt", (event, { x, y }) => {
      event.sender.focus();
      event.sender.sendInputEvent({ type: "mouseDown", x, y, button: "left", clickCount: 1 });
      event.sender.sendInputEvent({ type: "mouseUp", x, y, button: "left", clickCount: 1 });
    });
  }
  aiAutoReply() {
    electron.ipcMain.handle("aiAutoReply", async (_event, params) => {
      return await getAiReplyApi({
        question: params.question,
        dataset: queryParams.uuid,
        messageList: params.messageList,
        chat_id: params.chat_id
      });
    });
  }
  getWhatsappNameKey() {
    electron.ipcMain.handle("getWhatsappNameKey", (e, userId) => {
      const md5Buffer = crypto.createHash("md5").update(`${userId}@c.us:last-pushname`, "utf8").digest();
      const b64 = md5Buffer.toString("base64");
      return b64;
    });
  }
}
class AppApi {
  getAllViews() {
    electron.ipcMain.handle("getAllViews", (_event) => {
      let o = electron.BrowserWindow.getAllWindows();
      console.log(o);
      let w = electron.webContents.getAllWebContents();
      console.log(w);
      return "";
    });
  }
  getPlatform() {
    electron.ipcMain.handle("getPlatform", (_event) => {
      return utils.platform;
    });
  }
  initOnUnReadCount() {
    electron.ipcMain.on("setUnReadCount", (_event, params) => {
      try {
        GlobalObject.messageManager.setMessageCount(params);
      } catch (error) {
        console.warn("未读消息接受事件错误:", error);
      }
    });
  }
  sendToMainSySOver() {
    electron.ipcMain.on("sendToMainSySOver", (_event, arg) => {
      arg ? GlobalObject.mainWindow.maximize() : GlobalObject.mainWindow.unmaximize();
    });
  }
  sendToSySMin() {
    electron.ipcMain.on("sendToSySMin", (_event) => {
      GlobalObject.mainWindow.minimize();
    });
  }
  getAiIsSet() {
    electron.ipcMain.handle("getAiIsSet", async (e, params) => {
      try {
        var config = {
          method: "post",
          url: "http://aikf.lhyq360.com/app/ai/embedding/getDateset",
          headers: {
            "Content-Type": "application/json",
            "Accept": "*/*",
            "Host": "aikf.lhyq360.com",
            "Connection": "keep-alive"
          },
          data: JSON.stringify(params)
        };
        const res = await axios(config);
        console.log(res);
        return res.data?.data?.roleId ? true : false;
      } catch (error) {
        console.error(error);
      }
    });
  }
  sysSet() {
    electron.ipcMain.on("sysSet", (_event, data, type) => {
      if (type == "win") {
        Config.iscloseApp = data;
      } else if (type == "dark") {
        GlobalObject.mainWindow?.webContents.send("reloadIconUrl", data);
      } else if (type === "message") {
        Config.isShowMessage = data;
        if (!GlobalObject.messageManager) {
          return;
        }
        if (data) {
          GlobalObject.messageManager.showMessageCount();
        } else {
          GlobalObject.messageManager.hideMessageCount();
        }
      } else if (type === "ai" || type === "aiTimeRang") {
        Config[type] = data;
        console.log("config::", Config);
        if (GlobalObject.viewManager?.viewsMap) {
          GlobalObject.viewManager.viewsMap.forEach((view2) => {
            if (view2?.webContents) {
              view2.webContents.send("updateAiReplyConfig", { ai: data, aiTimeRang: data });
            }
          });
        }
      }
    });
  }
  quitApp() {
    electron.ipcMain.handle("quitApp", (event, arg) => {
      if (Config.iscloseApp) {
        GlobalObject.mainWindow.hide();
      } else {
        electron.app.quit();
      }
      return Config.iscloseApp;
    });
  }
  hideApp() {
    electron.ipcMain.on("hideApp", () => {
      GlobalObject.mainWindow.hide();
    });
  }
  confirmQuit() {
    electron.ipcMain.handle("confirmQuit", () => {
      const i = electron.dialog.showMessageBoxSync(GlobalObject.mainWindow, {
        type: "question",
        buttons: ["确定", "取消"],
        defaultId: 0,
        title: "退出提醒",
        message: "确认要退出吗?"
      });
      if (i === 0) {
        return true;
      } else {
        return false;
      }
    });
  }
}
class TransalteApi {
  pendingMap = /* @__PURE__ */ new Map();
  getTranslateConfig() {
    electron.ipcMain.handle("getTranslateConfig", async (event, { sessionId, isChating }) => {
      let config;
      if (sessionId && isChating) {
        config = await GlobalObject.translateDB.getTranslate({
          sessionId
        });
        if (!config) {
          config = GlobalObject.translateConfig;
          GlobalObject.translateDB.setTranslate({
            sessionId,
            config
          });
        }
      } else {
        config = GlobalObject.translateConfig;
      }
      return config || {};
    });
  }
  updateTranslateConfig() {
    electron.ipcMain.on("updateTranslateConfig", (event, { config, isChating, sessionId }) => {
      console.log("翻译配置更新", { config, isChating });
      if (isChating && sessionId) {
        GlobalObject.translateDB.setTranslate({
          sessionId,
          config
        });
        GlobalObject.viewManager.viewsMap.get(sessionId).webContents.send("updateViewTranslateConfig", config);
      } else {
        GlobalObject.translateConfig = config;
      }
    });
  }
  setTranslateConfig() {
    electron.ipcMain.on("setTranslateConfig", (event, { config, sessionId }) => {
      console.log("create config", config);
      if (sessionId) {
        GlobalObject.translateDB.setTranslate({
          sessionId,
          config
        });
      }
    });
  }
  googleAsyncTranslate() {
    electron.ipcMain.handle("googleAsyncTranslate", (_event, params) => {
      console.log("googleAsyncTranslate");
      if (params.text.length) return utils_googleTranslateApi.googleAsyncTranslate(params);
      return new Promise((resolve, reject) => {
        this.pendingMap.set(idCounter, { resolve, reject });
        worker.postMessage({ ...params, id: idCounter });
        idCounter += 1;
      });
    });
  }
}
function mountIpcApi() {
  const APIARR = [new TabViewApi(), new AppApi(), new TransalteApi()];
  APIARR.forEach((apiInstance) => {
    getAllMethods(apiInstance).forEach((method) => {
      apiInstance[method]();
    });
  });
}
async function initShortcut() {
  try {
    await electron.app.whenReady();
    electron.app.on("quit", () => {
      electron.globalShortcut.unregisterAll();
    });
  } catch (error) {
  }
}
class LowDB {
  constructor(file = "db.json", { defaultData } = {
    defaultData: {}
  }) {
    const adapter = new node.JSONFile(file);
    this.db = new lowdb.Low(adapter, defaultData);
  }
  async init() {
    try {
      await this.db.read();
      this.db.data ||= this.db.defaultData;
      await this.db.write();
    } catch (error) {
      this.db.data = {};
      await this.db.write();
    }
  }
  async save() {
    await this.db.write();
  }
  async getAll(collection) {
    await this.db.read();
    return this.db.data[collection] || [];
  }
  async getOne(collection, filter) {
    await this.db.read();
    const items = this.db.data[collection] || [];
    const item = items.find((i) => this.#matches(i, filter));
    return item;
  }
  async create(collection, item) {
    await this.db.read();
    this.db.data[collection] ||= [];
    this.db.data[collection].push(item);
    await this.db.write();
    return item;
  }
  async update(collection, filter, updates) {
    await this.db.read();
    const items = this.db.data[collection] || [];
    const item = items.find((i) => this.#matches(i, filter));
    if (item) {
      console.log("item", item);
      Object.assign(item, updates);
      console.log("upitem", item);
    }
    await this.db.write();
    return item;
  }
  async remove(collection, filter) {
    await this.db.read();
    const items = this.db.data[collection] || [];
    this.db.data[collection] = items.filter((item) => !this.#matches(item, filter));
    await this.db.write();
  }
  // 私有方法：对象匹配
  #matches(item, filter) {
    return Object.entries(filter).every(([key, value]) => item[key] === value);
  }
}
async function createOrOpenPath(filePath) {
  try {
    const stats = await fs.stat(filePath);
    if (stats.isDirectory()) {
      console.log(`目录已存在: ${filePath}`);
      return "directory_exists";
    } else {
      console.log(`文件已存在: ${filePath}`);
      return "file_exists";
    }
  } catch (error) {
    if (error.code === "ENOENT") {
      console.log(`路径不存在，正在创建: ${filePath}`);
      const ext = path.extname(filePath);
      if (ext) {
        const dir = path.dirname(filePath);
        await fs.mkdir(dir, { recursive: true });
        await fs.writeFile(filePath, "");
        console.log(`文件及其父目录已成功创建: ${filePath}`);
        return "file_created";
      } else {
        await fs.mkdir(filePath, { recursive: true });
        console.log(`目录及其父目录已成功创建: ${filePath}`);
        return "directory_created";
      }
    } else {
      console.error(`处理路径时发生错误: ${error.message}`);
      return "error";
    }
  }
}
class TranslateDB {
  constructor({ fileName } = { fileName: "translate.db.json" }) {
    this.init(fileName);
  }
  async init(fileName) {
    await createOrOpenPath(fileName);
    this.db = new LowDB(fileName);
    this.db.init();
  }
  // 根据 会话id 获取config
  async getTranslate({ sessionId }) {
    const res = await this.db.getOne("sessionIds", { sessionId });
    return res?.config;
  }
  async setTranslate({ sessionId, config }) {
    const r = await this.db.update("sessionIds", { sessionId }, { sessionId, config });
    if (!r) {
      await this.db.create("sessionIds", { sessionId, config });
    }
  }
}
const NETWORK_ERRORS = [
  "Network Error",
  "NetworkError",
  "Failed to fetch",
  "fetch is not defined",
  "XMLHttpRequest",
  "ERR_NETWORK",
  "ERR_INTERNET_DISCONNECTED",
  "ERR_CONNECTION_REFUSED",
  "ERR_CONNECTION_RESET",
  "ERR_CONNECTION_TIMED_OUT",
  "ERR_NAME_NOT_RESOLVED",
  "ENOTFOUND",
  "ECONNREFUSED",
  "ECONNRESET",
  "ETIMEDOUT",
  "ENETUNREACH",
  "EHOSTUNREACH",
  "timeout",
  "Request timeout",
  "net::ERR_",
  "DNS_PROBE_FINISHED_NXDOMAIN",
  "DNS_PROBE_FINISHED_NO_INTERNET"
];
const BROWSER_COMPATIBILITY_ERRORS = [
  "ResizeObserver loop limit exceeded",
  "ResizeObserver loop completed with undelivered notifications",
  "Non-Error promise rejection captured",
  "Script error",
  "Object Not Found Matching Id",
  "IntersectionObserver",
  "MutationObserver",
  "PerformanceObserver"
];
const USER_ACTION_ERRORS = [
  "AbortError",
  "The user aborted a request",
  "Request was cancelled",
  "cancelled",
  "canceled",
  "User cancelled",
  "Operation was aborted",
  "The operation was aborted"
];
const PERMISSION_ERRORS = [
  "Permission denied",
  "Permissions policy violation",
  "NotAllowedError",
  "SecurityError",
  "Access denied",
  "Blocked by CORS policy",
  "Cross-Origin Request Blocked"
];
const THIRD_PARTY_ERRORS = [
  "facebook.com",
  "google-analytics.com",
  "googletagmanager.com",
  "googlesyndication.com",
  "doubleclick.net",
  "extension://",
  "chrome-extension://",
  "moz-extension://",
  "safari-extension://"
];
const DEVELOPMENT_ERRORS = [
  "HMR",
  "Hot Module Replacement",
  "webpack-dev-server",
  "webpack-internal",
  "[HMR]",
  "hot-update"
];
const RESOURCE_LOADING_ERRORS = [
  "Loading chunk",
  "Loading CSS chunk",
  "ChunkLoadError",
  "Failed to import",
  "Module not found",
  "Cannot resolve module",
  "Dynamic import"
];
const OTHER_FILTERED_ERRORS = [
  "Non-Error exception captured",
  "Uncaught (in promise)",
  "Promise rejected with no error",
  "undefined is not an object",
  "null is not an object",
  "Cannot read property of undefined",
  "Cannot read property of null"
];
const ALL_FILTERED_ERRORS = [
  ...NETWORK_ERRORS,
  ...BROWSER_COMPATIBILITY_ERRORS,
  ...USER_ACTION_ERRORS,
  ...PERMISSION_ERRORS,
  ...THIRD_PARTY_ERRORS,
  ...DEVELOPMENT_ERRORS,
  ...RESOURCE_LOADING_ERRORS,
  ...OTHER_FILTERED_ERRORS
];
const ERROR_FILTER_PATTERNS = [
  // 脚本错误
  /^Script error\.?$/,
  /^Javascript error: Script error\.? on line 0$/,
  // ResizeObserver 相关
  /^ResizeObserver loop/,
  // 资源加载错误
  /^Loading chunk \d+ failed/,
  /^Loading CSS chunk \d+ failed/,
  /^ChunkLoadError/,
  // 网络错误 (不区分大小写)
  /network|Network|NETWORK/i,
  /timeout|Timeout|TIMEOUT/i,
  /fetch|Fetch|FETCH.*failed/i,
  /connection|Connection|CONNECTION.*(refused|reset|timed|failed)/i,
  /ENOTFOUND|ECONNREFUSED|ECONNRESET|ETIMEDOUT|ENETUNREACH|EHOSTUNREACH/,
  /ERR_NETWORK|ERR_INTERNET|ERR_CONNECTION|ERR_NAME_NOT_RESOLVED/,
  /net::ERR_/,
  /DNS_PROBE_FINISHED/,
  // 用户取消操作
  /cancelled|canceled|abort/i,
  // 权限错误
  /permission.*denied/i,
  /not.*allowed/i,
  /access.*denied/i,
  /blocked.*by.*cors/i,
  // 第三方和扩展
  /extension:\/\//,
  /chrome-extension/,
  /moz-extension/,
  /safari-extension/,
  // 开发环境
  /webpack|HMR|hot.*module/i,
  // 其他常见错误
  /undefined.*is.*not.*an.*object/i,
  /null.*is.*not.*an.*object/i,
  /cannot.*read.*property.*of.*(undefined|null)/i
];
function shouldFilterByFilename(filename) {
  if (!filename) return false;
  const filterPatterns = [
    "facebook.com",
    "google-analytics.com",
    "googletagmanager.com",
    "googlesyndication.com",
    "doubleclick.net",
    "extension://",
    "chrome-extension://",
    "moz-extension://",
    "safari-extension://",
    "webpack-internal"
  ];
  return filterPatterns.some((pattern) => filename.includes(pattern));
}
function shouldFilterByMessage(message) {
  if (!message) return false;
  for (const filteredError of ALL_FILTERED_ERRORS) {
    if (message.includes(filteredError)) {
      return true;
    }
  }
  for (const pattern of ERROR_FILTER_PATTERNS) {
    if (pattern.test(message)) {
      return true;
    }
  }
  return false;
}
function shouldFilterByType(errorType) {
  if (!errorType) return false;
  const filteredTypes = [
    "NetworkError",
    "AbortError",
    "SecurityError",
    "NotAllowedError",
    "ChunkLoadError"
  ];
  return filteredTypes.includes(errorType);
}
function getSentryDSN(processType) {
  try {
    if (processType === "renderer") ;
    else {
      return "https://<EMAIL>/4509942731112528";
    }
  } catch (error) {
    return "https://<EMAIL>/4509942731112528";
  }
}
function getAppVersion(processType = "unknown") {
  try {
    if (processType === "main") {
      const { app } = require("electron");
      return app.getVersion();
    } else if (processType === "renderer") {
      return "2.1.3";
    } else {
      return "2.1.3";
    }
  } catch (error) {
    console.warn(`Failed to get version for ${processType}:`, error);
    return "2.1.3";
  }
}
function getEnvironment(processType = "unknown") {
  try {
    if (processType === "main") {
      return process.env.NODE_ENV === "production" ? "production" : "development";
    } else {
      return true ? "production" : "development";
    }
  } catch (error) {
    return "production";
  }
}
function shouldFilterError(event) {
  if (event.exception?.values) {
    for (const exception of event.exception.values) {
      const errorMessage = exception.value || "";
      const errorType = exception.type || "";
      if (shouldFilterByMessage(errorMessage) || shouldFilterByType(errorType)) {
        return true;
      }
      if (exception.stacktrace?.frames) {
        for (const frame of exception.stacktrace.frames) {
          const filename = frame.filename || "";
          if (shouldFilterByFilename(filename)) {
            return true;
          }
        }
      }
    }
  }
  if (event.message && shouldFilterByMessage(event.message)) {
    return true;
  }
  if (event.level === "info" || event.level === "debug") {
    return true;
  }
  if (event.tags) {
    const filteredTags = ["test", "development-only", "ignore"];
    for (const tag of filteredTags) {
      if (event.tags[tag]) {
        return true;
      }
    }
  }
  return false;
}
function createBeforeSend(processType) {
  return function(event) {
    if (shouldFilterError(event)) {
      if (getEnvironment(processType) === "development") {
        console.log(
          `[Sentry ${processType}] Filtered error:`,
          event.exception?.values?.[0]?.value || event.message
        );
      }
      return null;
    }
    event.tags = {
      ...event.tags,
      process: processType,
      platform: process.platform,
      version: getAppVersion(processType)
    };
    event.user = {
      ...event.user,
      process: processType
    };
    event.contexts = {
      ...event.contexts,
      app: {
        name: "LanHai Translator",
        version: getAppVersion(processType)
      },
      runtime: {
        name: "electron-main",
        version: process.versions?.electron || "unknown"
      }
    };
    if (getEnvironment(processType) === "development") {
      console.log(`[Sentry ${processType}] Sending event:`, {
        message: event.message,
        error: event.exception?.values?.[0]?.value,
        tags: event.tags
      });
    }
    return event;
  };
}
function getMainConfig() {
  return {
    dsn: getSentryDSN("main"),
    release: getAppVersion("main"),
    environment: getEnvironment("main"),
    beforeSend: createBeforeSend("main")
  };
}
const __vite_import_meta_env__ = { "BASE_URL": "/", "DEV": false, "MODE": "production", "PROD": true, "SSR": true, "VITE_APP_VERSION": "2.1.3", "VITE_BASE_API": "https://lhyt.blueglob.com", "VITE_BASE_URL": "/", "VITE_PLATFROM_HOST_NAMES": '["x.com", "www.facebook.com", "zalo", "telegram", "whatsapp", "instagram", "tiktok", "viber", "business.facebook.com", "discord.com"]', "VITE_SENTRY_AUTH_TOKEN": "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", "VITE_SENTRY_DSN": "https://<EMAIL>/4509942731112528", "VITE_SENTRY_ORG": "senlg", "VITE_SENTRY_PROJECT": "4509942731112528" };
Sentry.init(getMainConfig());
console.log("import.meta.env", __vite_import_meta_env__);
const preloadJsPathMap = /* @__PURE__ */ new Map([
  ["facebook", path.join(__dirname, "../preload/index.js")],
  ["instagram", path.join(__dirname, "../preload/index.js")],
  ["telegram", path.join(__dirname, "../preload/index.js")],
  ["whatsapp", path.join(__dirname, "../preload/index.js")],
  ["zalo", path.join(__dirname, "../preload/index.js")],
  ["tiktok", path.join(__dirname, "../preload/index.js")],
  ["facebookBusiness", path.join(__dirname, "../preload/index.js")],
  ["twitter", path.join(__dirname, "../preload/index.js")],
  ["discord", path.join(__dirname, "../preload/index.js")]
]);
process.on("uncaughtException", (error) => {
  console.error("[MainProcess UncaughtException]", error);
});
process.on("unhandledRejection", (reason) => {
  console.error("[MainProcess UnhandledRejection]", reason);
});
({
  enableLogging: !electron.app.isPackaged
});
const queryParams = {
  uuid: ""
};
try {
  let getAllWebViews = function() {
    const webViews = [];
    const windows = electron.BrowserWindow.getAllWindows();
    windows.forEach((win) => {
      if (win.webContents) {
        const webContents = win.webContents;
        if (webContents && webContents.debugger && webContents.getWebRTCIPHandlingPolicy) {
        }
      }
    });
    return webViews;
  };
  const getSelectorInfo = async (accessToken) => {
    const res = await getSelectorInfoFunc({}, accessToken);
    const r = {
      facebook: {
        userElSelector: "div.x1iyjqo2>div>ul>li:nth-child(1) a",
        sessionUserA: `div[data-pagelet="MWThreadList"]>div.x1qjc9v5.x9f619.xdl72j9.x2lwn1j.xeuugli.x1n2onr6.x1ja2u2z.x78zum5.xdt5ytf.x1iyjqo2.xs83m0k.x6ikm8r.x10wlt62 a[role='link']`,
        sessionUserNickNameElSelector: "a[role='link'] [class='x1lliihq x6ikm8r x10wlt62 x1n2onr6 xlyipyv xuxw1ft']",
        unreadElSelector: "span.x1spa7qu",
        sessionListElSelector: 'div[data-pagelet="MWThreadList"]>div.x1qjc9v5.x9f619.xdl72j9.x2lwn1j.xeuugli.x1n2onr6.x1ja2u2z.x78zum5.xdt5ytf.x1iyjqo2.xs83m0k.x6ikm8r.x10wlt62',
        sendButtonElSelector: "div.xuk3077.x57kliw.x78zum5.x6prxxf.xz9dl7a.xsag5q8>span.html-span:last-child",
        sendButtonElSelector: 'div.xuk3077.x57kliw.x78zum5.x6prxxf.xz9dl7a.xsag5q8>span.html-span:last-child div[role="button"]',
        inputElSelector: "div.notranslate",
        messageListElSelector: "[class='x78zum5 xdt5ytf x1iyjqo2 x6ikm8r x1odjw0f xish69e x16o0dkt']",
        reciveMessageElSelector: '[class="html-div xexx8yu xyri2b x18d9i69 x1c1uobl x1gslohp x14z9mp x12nagc x1lziwak x1yc453h x126k92a x18lvrbx"]',
        sendMessageElSelector: '[class="html-div xexx8yu xyri2b x18d9i69 x1c1uobl x1gslohp x14z9mp x12nagc x1lziwak x1yc453h x126k92a xyk4ms5"]',
        // 小窗
        activeSmall: ".x1eqhsl0",
        smallInputElSelector: "div.xzsf02u.x1a2a7pz.x1n2onr6.x14wi4xw",
        smallSendButtonElSelector: '[aria-label="按 Enter 键发送"]',
        smallBtnParent: "",
        activeSmallId: 'a[class="x1i10hfl x1qjc9v5 xjbqb8w xjqpnuy xc5r6h4 xqeqjp1 x1phubyo x13fuv20 x18b5jzi x1q0q8m5 x1t7ytsu x972fbf x10w94by x1qhh985 x14e42zd x9f619 x1ypdohk xdl72j9 x2lah0s xe8uvvx xdj266r x14z9mp xat24cr x1lziwak x2lwn1j xeuugli xexx8yu xyri2b x18d9i69 x1c1uobl x1n2onr6 x16tdsg8 x1hl2dhg xggy1nq x1ja2u2z x1t137rt x1fmog5m xu25z0z x140muxe xo1y3bh x1q0g3np x87ps6o x1lku1pv x1a2a7pz x78zum5"]',
        activeSmallNickName: ".xxymvpz.x1dyh7pn"
      },
      instagram: {
        userElSelector: 'div[class="x1i10hfl xjbqb8w x1ejq31n xd10rxx x1sy0etr x17r0tee x972fbf xcfux6l x1qhh985 xm0m39n x9f619 x1ypdohk xe8uvvx xdj266r x11i5rnm xat24cr x1mh8g0r xexx8yu x4uap5 x18d9i69 xkhd6sd x16tdsg8 x1hl2dhg xggy1nq x1a2a7pz x6s0dn4 x78zum5 xs83m0k xeuugli x1d5wrs8"]',
        sessionUserNickNameElSelector: `div[class="x9f619 xjbqb8w x78zum5 x168nmei x13lgxp2 x5pf9jr xo71vjh x1uhb9sk x1plvlek xryxfnj x1c4vz4f x2lah0s x1q0g3np xqjyukv x6s0dn4 x1oa3qoh x1nhvcw1"]`,
        sessionCurrentUserElSelector: `div[class="x1i10hfl x1qjc9v5 xjqpnuy xa49m3k xqeqjp1 x2hbi6w x13fuv20 xu3j5b3 x1q0q8m5 x26u7qi x972fbf xcfux6l x1qhh985 xm0m39n x9f619 x1ypdohk xdl72j9 x2lah0s xe8uvvx x2lwn1j xeuugli x1n2onr6 x16tdsg8 x1hl2dhg xggy1nq x1ja2u2z x1t137rt x1q0g3np x87ps6o x1lku1pv x1a2a7pz x168nmei x13lgxp2 x5pf9jr xo71vjh x1lliihq xdj266r x11i5rnm xat24cr x1mh8g0r x1y1aw1k xwib8y2 xbbxn1n xxbr6pl x19g9edo xmoo3j9 x1e9wozp xoo1adm"]`,
        unreadElSelector: ".x9f619.x1ja2u2z.xzpqnlu.x1hyvwdk.x14bfe9o.xjm9jq1.x6ikm8r.x10wlt62.x10l6tqk.x1i1rx1s",
        sessionListElSelector: "div.x78zum5.xdt5ytf.x1iyjqo2.x6ikm8r.x10wlt62.x1n2onr6.x1xzczws",
        sendButtonElSelector: 'div[class="x1i10hfl xjqpnuy xa49m3k xqeqjp1 x2hbi6w xdl72j9 x2lah0s xe8uvvx xdj266r x1mh8g0r x2lwn1j xeuugli x1hl2dhg xggy1nq x1ja2u2z x1t137rt x1q0g3np x1lku1pv x1a2a7pz x6s0dn4 xjyslct x1ejq31n xd10rxx x1sy0etr x17r0tee x9f619 x1ypdohk x1f6kntn xwhw2v2 xl56j7k x17ydfre x2b8uid xlyipyv x87ps6o x14atkfc xcdnw81 x1i0vuye xjbqb8w xm3z3ea x1x8b98j x131883w x16mih1h x972fbf xcfux6l x1qhh985 xm0m39n xt0psk2 xt7dq6l xexx8yu x4uap5 x18d9i69 xkhd6sd x1n2onr6 x1n5bzlp x173jzuc x1yc6y37 x7fd4wk x1sfzahb xfs2ol5"]',
        inputElSelector: "div.xzsf02u.x1a2a7pz.x1n2onr6.x14wi4xw.x1iyjqo2.x1gh3ibb.xisnujt.xeuugli.x1odjw0f.notranslate",
        messageListElSelector: "div.x1uipg7g.xu3j5b3.xol2nv.xlauuyb.x26u7qi.x19p7ews.x78zum5.xdt5ytf.x1iyjqo2.x6ikm8r.x10wlt62",
        reciveMessageElSelector: "div.x1uipg7g.xu3j5b3.xol2nv.xlauuyb.x26u7qi.x19p7ews.x78zum5.xdt5ytf.x1iyjqo2.x6ikm8r.x10wlt62 span.x193iq5w.xeuugli.x13faqbe.x1vvkbs.x1xmvt09.x1lliihq.x1s928wv.xhkezso.x1gmr53x.x1cpjm7i.x1fgarty.x1943h6x.xudqn12.x3x7a5m.x6prxxf.xvq8zen.xo1l8bm.xzsf02u",
        sendMessageElSelector: "div.x1uipg7g.xu3j5b3.xol2nv.xlauuyb.x26u7qi.x19p7ews.x78zum5.xdt5ytf.x1iyjqo2.x6ikm8r.x10wlt62 span.x193iq5w.xeuugli.x13faqbe.x1vvkbs.x1xmvt09.x1lliihq.x1s928wv.xhkezso.x1gmr53x.x1cpjm7i.x1fgarty.x1943h6x.xudqn12.x3x7a5m.x6prxxf.xvq8zen.xo1l8bm.x14ctfv"
      },
      telegram: {
        messageListElSelector: ".has-sticky-dates",
        // 自己发送的
        reciveMessageElSelector: ".bubble.is-out .message.spoilers-container",
        // 对方发送的
        sendMessageElSelector: ".bubble.is-in .message.spoilers-container .translatable-message",
        // 当前active的聊天人
        activeChat: "a.chatlist-chat.active",
        // 聊天列表
        chatList: "ul.chatlist a.chatlist-chat",
        // 滚动区域
        scrollContent: ".bubbles.has-groups.has-sticky-dates .scrollable.scrollable-y",
        // 输入框
        input: '.input-message-container [contenteditable="true"]',
        inputContent: '.input-message-container [contenteditable="true"]',
        // 发送按钮
        sendBtn: ".btn-send-container button",
        // 发送按钮遮罩层父级
        maskDependEl: ".btn-send-container",
        // 新粉丝名
        newFansNickName: '[data-only-first-name="0"]'
      },
      whatsapp: {
        searchListSelector: 'span[class="x10l6tqk x13vifvy xtijo5x x1ey2m1c x1o0tod"]',
        chatListContainerSelector: "#pane-side",
        scrollElSelector: "[class='x10l6tqk x13vifvy x1o0tod xyw6214 x9f619 x78zum5 xdt5ytf xh8yej3 x5yr21d x6ikm8r x1rife3k xjbqb8w x1ewm37j']",
        dividerElSelector: "hr[class='xvy4d1p xjm9jq1 xamhcws x13fuv20 xxdxl9i x18oe1m7 x1sy0etr xstzfhl xw7yly9']",
        // 用户名
        nickNameElSelector: ".x1iyjqo2.x6ikm8r.x10wlt62.x1n2onr6.xlyipyv.x1fj9vlw._ao3e",
        shopOwnersNickNameElSelector: "div[class='xs83m0k x1g77sc7 xeuugli x2lwn1j xozqiw3 x1oa3qoh x12fk4p8 x1iyjqo2 x1t1x2f9 x6ikm8r x10wlt62 x37zpob xg83lxy']",
        // 消息列表
        messageListElSelector: '#main [data-tab="8"]',
        // 自己发送的
        reciveMessageElSelector: ".message-out ._akbu span._ao3e",
        // 对方发送的
        sendMessageElSelector: ".message-in ._akbu span._ao3e",
        // 聊天列表
        chatList: '[role="grid"] [role="listitem"]',
        // 未读消息
        unread: '[aria-label*="条未读消息"]',
        // 未读消息phone
        unreadPhone: '[aria-colindex="2"] div._ak8q',
        // 滚动区域
        scrollContent: "[class='x10l6tqk x13vifvy x1o0tod xyw6214 x9f619 x78zum5 xdt5ytf xh8yej3 x5yr21d x6ikm8r x1rife3k xjbqb8w x1ewm37j']",
        // 在localStorage获取WS账号信息的key
        userInfoKey: "last-wid-md",
        // 当前在选中状态的聊天对象
        activeChat: '[aria-selected="true"]',
        activeChatId: '[aria-selected="true"] [aria-colindex="2"] span[dir="auto"]',
        // 当前聊天是个人
        personalChat: '[role="grid"] [aria-selected="true"] [aria-colindex="2"]',
        // 是频道
        channelChat: '[aria-label="频道列表"] [aria-selected="true"] [aria-colindex="2"]',
        // 发送按钮
        sendBtn: '[data-icon="wds-ic-send-filled"]',
        sendBtnBusiness: '#main button>span[data-icon="send"]',
        // 发送按钮遮罩层父级
        maskDependIsMerchantEl: "._ak1q div.x123j3cw",
        // 发送按钮遮罩层父级 非商户
        maskDependEl: '[class="x9f619 x78zum5 x6s0dn4 xl56j7k xpvyfi4 x2lah0s x1c4vz4f x1fns5xo x1ba4aug x14yjl9h xudhj91 x18nykt9 xww2gxu x1pse0pq x8j4wrb"]',
        // 输入框
        input: '[tabindex="10"]',
        inputContent: 'div[tabindex="10"] span[data-lexical-text="true"]',
        // 附件发送按钮
        attSendBtn: '[aria-label="发送"]',
        // 附件发送按钮遮罩层父级
        attMaskDependEl: "div.x1247r65",
        // 附件输入框
        attInput: '[aria-placeholder="添加说明"]',
        attInputContent: '[aria-placeholder="添加说明"] span[data-lexical-text="true"]'
      },
      tiktok: {
        userIdElSelector: 'a[data-e2e="nav-profile"]',
        inputElSelector: '[class="notranslate public-DraftEditor-content"]',
        inputTextElSelector: '[class="public-DraftStyleDefault-block public-DraftStyleDefault-ltr"]>span>span',
        currentSessionElSelector: "[class='css-1ojajeq-DivItemWrapper eii3f6d3']",
        sessionUserNameElSelector: "p[class='css-1l1cwdw-PInfoNickname eii3f6d9']",
        messageListElSelector: "[class='css-okl125-DivChatMainContent ediam1h26']",
        unreadElSelector: "[class='css-1xdqxu2-SpanNewMessage eii3f6d2']",
        sessionElSelector: '[class="css-1rrx3i5-DivItemWrapper eii3f6d3"]',
        sendButtonElSelector: '[class="css-d7yhdo-StyledSendButton e1823izs3"]',
        sendButtonContainerElSelector: `[class="css-z1eu53-DivMessageInputAndSendButton e1823izs1"]`,
        reciveMessageElSelector: '[class="css-1e0nj4n-DivMessageHorizontalContainer e9j91388"] p[class="css-1rdxtjl-PText e9j913815"]',
        sendMessageElSelector: '[class="css-wvg5vm-DivMessageHorizontalContainer e9j91388"] p[class="css-1rdxtjl-PText e9j913815"]'
      },
      facebookBusiness: {
        userNameSelector: '[class="x78zum5 x2lwn1j xeuugli xh8yej3"]',
        inputElSelector: 'textarea[type="text"]',
        sessionUserNameElSelector: '[class="x78zum5 x1q0g3np x2lwn1j xeuugli"]',
        currentSessionUserNameElSelector: "[class='x1swdo50 xw2csxc x1odjw0f _5bpf']",
        messageListElSelector: "[class='x1yrsyyn x5zjp28 x10b6aqq xwn43p0']",
        unreadElSelector: "._5m10._284c ._5hhj",
        sessionElSelector: '[class="xh8yej3 x2izyaf"] [role="presentation"]',
        sendButtonElSelector: '[class="xogfrqt x1jchvi3"]>div',
        sendButtonContainerElSelector: `[class="x6s0dn4 x78zum5 x2lwn1j xeuugli"]`,
        reciveMessageElSelector: '[class="xuk3077 x78zum5 x1q0g3np x2lwn1j xeuugli x193iq5w xh8yej3"] div[class="x1rg5ohu x67bb7w"]>div>span:first-child',
        sendMessageElSelector: '[class="xuk3077 x78zum5 x1q0g3np x13a6bvl x2lwn1j xeuugli x1f6kntn xlxfd2w x1gslohp x126k92a"] div[class="x1rg5ohu x67bb7w"]>div>span:first-child'
      },
      twitter: {
        userElSelector: "nav>a:last-of-type",
        inputElSelector: "div.DraftEditor-editorContainer>div",
        inputTextElSelector: "div.public-DraftStyleDefault-block>span[data-offset-key]>span",
        sessionUserNameElSelector: 'div[class="css-146c3p1 r-dnmrzs r-1udh08x r-1udbk01 r-3s2u2q r-bcqeeo r-1ttztb7 r-qvutc0 r-37j5jr r-a023e6 r-rjixqe r-16dba41 r-18u37iz r-1wvb978"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]',
        currentSessionUserNameElSelector: `.r-x572qd div[class="css-146c3p1 r-dnmrzs r-1udh08x r-1udbk01 r-3s2u2q r-bcqeeo r-1ttztb7 r-qvutc0 r-37j5jr r-a023e6 r-rjixqe r-16dba41 r-18u37iz r-1wvb978"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]`,
        messageListElSelector: "[class='css-175oi2r r-16y2uox r-10m9thr r-1h0z5md r-f8sm7e r-13qz1uu r-3pj75a r-1ye8kvj']",
        unreadElSelector: ".r-l5o3uw",
        sessionElSelector: 'div[role="tablist"]',
        sendButtonElSelector: 'button[class="css-175oi2r r-sdzlij r-1phboty r-rs99b7 r-lrvibr r-1ez5h0i r-2yi16 r-1qi8awa r-1loqt21 r-o7ynqc r-6416eg r-1ny4l3l"]',
        sendButtonContainerElSelector: `[class="css-175oi2r r-1awozwy r-1sw30gj r-1867qdf r-18u37iz r-l00any r-jusfrs r-tuq35u r-1h0ofqe"]`,
        reciveMessageElSelector: 'div[class="css-175oi2r r-1habvwh r-1wbh5a2"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]',
        sendMessageElSelector: 'div[class="css-175oi2r r-obd0qt r-1wbh5a2"] span[class="css-1jxf684 r-bcqeeo r-1ttztb7 r-qvutc0 r-poiln3"]'
      }
    };
    if (!electron.app.isPackaged) {
      GlobalObject.selectorInfo = r;
    } else {
      if (res.code === 1) {
        GlobalObject.selectorInfo = res.data;
      }
    }
  };
  async function init2() {
    GlobalObject.translateDB = new TranslateDB({
      fileName: path.join(electron.app.getPath("userData"), "production", "translate.db.json")
    });
    GlobalObject.mainWindow = createWindow();
    if (!GlobalObject.mainWindow) {
      console.error("Failed to create main window");
      return;
    }
    GlobalObject.messageManager = new MessageManager();
    GlobalObject.viewManager = new ViewManager();
    initShortcut();
  }
  const gotTheLock = electron.app.requestSingleInstanceLock();
  if (!gotTheLock) {
    electron.app.quit();
  } else {
    electron.app.on("second-instance", (event, commandLine, workingDirectory) => {
      if (GlobalObject.mainWindow) {
        if (GlobalObject.mainWindow.isMinimized()) mainWindow.restore();
        GlobalObject.mainWindow.focus();
        GlobalObject.mainWindow.show();
      }
    });
  }
  electron.app.whenReady().then(async () => {
    if (utils.platform.isMacOS) {
      try {
        const status = electron.systemPreferences.getMediaAccessStatus("microphone");
        if (status === "not-determined") {
          await electron.systemPreferences.askForMediaAccess("microphone");
        }
      } catch (error) {
        console.error("Failed to request microphone permission:", error);
      }
    }
    electron.app.commandLine.appendSwitch("wm-window-animations-disabled");
    electron.app.on("browser-window-created", (_, window2) => {
      utils.optimizer.watchWindowShortcuts(window2);
    });
    mountIpcApi();
    await init2();
    let ai_open_num = 0;
    electron.ipcMain.on("open-new-window", (event, url2) => {
      if (ai_open_num == 0) {
        ai_open_num = 1;
        const iconPath = path.join(__dirname, "../../ai.png");
        const childWindow = new electron.BrowserWindow({
          width: 900,
          height: 600,
          icon: iconPath,
          autoHideMenuBar: true,
          parent: GlobalObject.mainWindow,
          // 使子窗口依赖于主窗口
          // modal: true, // 模态窗口，关闭主窗口时会关闭子窗口
          webPreferences: { nodeIntegration: false }
        });
        childWindow.setTitle("蓝海AI");
        childWindow.loadURL(url2);
        childWindow.webContents.on("did-finish-load", () => {
          childWindow.setTitle("蓝海AI");
        });
        childWindow.on("closed", () => {
          ai_open_num = 0;
        });
      }
    });
    utils.electronApp.setAppUserModelId("com.lanhaiyitong");
    let accessToken = "";
    electron.ipcMain.on("getUserLoginToken", (event, { token, uuid }) => {
      accessToken = token;
      queryParams.uuid = uuid;
      getDatasetRolePrompt({ dataset: uuid });
      getSelectorInfo(accessToken);
    });
    electron.ipcMain.on("empty-translate-config", (event, arg) => {
      GlobalObject.mainWindow.webContents.send("empty-translate-config-to-vue", arg);
    });
    electron.ipcMain.on("sendFansList", (event, arg) => {
      GlobalObject.mainWindow.webContents.send("sendFansList", arg);
    });
    electron.ipcMain.on("sendWhatsFansList", (event, arg) => {
      electron.BrowserWindow.getAllWindows().forEach((win) => {
        if (win.webContents !== event.sender) {
          win.webContents.send("getWhatsFansList", arg);
        }
      });
    });
    electron.ipcMain.on("sendWsActiveFanId", (event, arg) => {
      electron.BrowserWindow.getAllWindows().forEach((win) => {
        if (win.webContents !== event.sender) {
          win.webContents.send("sendWsActiveFanId", arg);
        }
      });
    });
    electron.ipcMain.on("sendZaloActiveFanId", (event, arg) => {
      electron.BrowserWindow.getAllWindows().forEach((win) => {
        if (win.webContents !== event.sender) {
          win.webContents.send("sendZaloActiveFanId", arg);
        }
      });
    });
    electron.ipcMain.on("sendInsActiveFanId", (event, arg) => {
      electron.BrowserWindow.getAllWindows().forEach((win) => {
        if (win.webContents !== event.sender) {
          win.webContents.send("sendInsActiveFanId", arg);
        }
      });
    });
    electron.ipcMain.on("sendFbActiveFanId", (event, arg) => {
      GlobalObject.mainWindow.webContents.send("sendFbActiveFanId", arg);
    });
    electron.ipcMain.on("sendActiveFanId", (event, arg) => {
      electron.BrowserWindow.getAllWindows().forEach((win) => {
        if (win.webContents !== event.sender) {
          win.webContents.send("getActiveFanId", arg);
        }
      });
    });
    electron.ipcMain.on("receive_trans_info", async (event, inputText, index, sonIndex, in_out) => {
      const res = await getTranslate(JSON.parse(inputText), accessToken);
      setTimeout(function() {
        event.reply(
          "translate-over-in",
          JSON.stringify(res),
          index,
          sonIndex,
          in_out,
          JSON.parse(inputText).text
        );
      }, 50);
    });
    electron.ipcMain.on("click_trans_info", async (event, inputText) => {
      const res = await getTranslate(JSON.parse(inputText), accessToken);
      setTimeout(function() {
        event.reply("translate-over-click", JSON.stringify(res));
      }, 50);
    });
    let localChatLog = "";
    electron.ipcMain.on("getLocalChatLog", (event, inputText) => {
      localChatLog = JSON.parse(inputText);
    });
    electron.ipcMain.on("getSaveChatLog", async (event, message) => {
      let chatHistoryArray = JSON.parse(message);
      console.log(chatHistoryArray, "get_infoget_infoget_info");
      const chatHistoryData = chatHistoryArray.map((i) => ({
        accountId: i.ccountId,
        fansId: i.fansId,
        chatType: i.type == "out" ? 1 : 2,
        originalText: i.text,
        translateText: i.translateText,
        sendTime: i.time,
        toLang: i.toLang,
        platform: i.platform,
        fromLang: i.fromLang || "auto"
      }));
      await saveChat(chatHistoryData, accessToken);
    });
    electron.ipcMain.handle("getMessageTranslate", async (event, message) => {
      try {
        let get_info = JSON.parse(message);
        console.log("main", message);
        let translateInfo = {
          text: get_info.text,
          engineCode: get_info.engineCode,
          fromLang: get_info.fromLang,
          toLang: get_info.toLang
        };
        let res = {};
        if (get_info.isInput && get_info.toLang === get_info.recive_lang) {
          res = { code: 1, data: { result: get_info.text } };
        } else {
          res = await getTranslate(translateInfo, accessToken);
          console.log("翻译结果%o \n", res);
          if (res.code == 400010) {
            GlobalObject.mainWindow.send("noMoney", res.msg);
          }
        }
        return res;
      } catch (error) {
        console.error("翻译内容报错", error);
        return {};
      }
    });
    electron.ipcMain.on("newFansAdd", (event, arg) => {
      electron.BrowserWindow.getAllWindows().forEach((win) => {
        if (win.webContents !== event.sender) {
          win.webContents.send("newFansInsert", arg);
        }
      });
    });
    electron.ipcMain.on("clearS", (event, arg) => {
      clearAllCredentials();
    });
    electron.app.on("activate", function() {
      if (electron.BrowserWindow.getAllWindows().length === 0) {
        GlobalObject.mainWindow = createWindow();
      } else {
        if (!GlobalObject.mainWindow || GlobalObject.mainWindow.isDestroyed()) {
          const windows = electron.BrowserWindow.getAllWindows();
          GlobalObject.mainWindow = windows[0];
        }
      }
    });
  });
  async function clearAllCredentials() {
    const defaultSession = electron.session.defaultSession;
    const allSessions = [defaultSession];
    const webViews = getAllWebViews();
    webViews.forEach((webView) => {
      if (webView.getWebContents && !allSessions.includes(webView.getWebContents().session)) {
        allSessions.push(webView.getWebContents().session);
        GlobalObject.mainWindow.contentView.removeChildView(view);
        GlobalObject.viewManager.deleteView(id);
        if (!webView.isDestroyed()) {
          webView.close();
        }
      }
    });
    for (const ses of allSessions) {
      try {
        await ses.clearStorageData({
          storages: [
            "cookies",
            "localstorage",
            "shadercache",
            "websql",
            "serviceworkers",
            "cachestorage"
          ],
          quotas: ["temporary", "persistent", "syncable"]
        });
        await ses.clearAuthCache();
        await ses.clearHostResolverCache();
        await ses.clearCache();
      } catch (error) {
        console.error(`清除会话数据失败: ${error.message}`);
      }
    }
  }
} catch (error) {
  console.log(error);
}
exports.preloadJsPathMap = preloadJsPathMap;
exports.queryParams = queryParams;
//# sourceMappingURL=index.js.map
