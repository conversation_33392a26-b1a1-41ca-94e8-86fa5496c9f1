"use strict";(function(){try{var t=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{};t.SENTRY_RELEASE={id:"2.1.3"}}catch{}})();try{(function(){var t=typeof window<"u"?window:typeof global<"u"?global:typeof globalThis<"u"?globalThis:typeof self<"u"?self:{},e=new t.Error().stack;e&&(t._sentryDebugIds=t._sentryDebugIds||{},t._sentryDebugIds[e]="67c08243-96f0-423e-bf64-17196d13ac10",t._sentryDebugIdIdentifier="sentry-dbid-67c08243-96f0-423e-bf64-17196d13ac10")})()}catch{}Object.defineProperty(exports,Symbol.toStringTag,{value:"Module"});const c=require("idb"),a=require("crypto-js");function u(t){const e=Object.create(null,{[Symbol.toStringTag]:{value:"Module"}});if(t){for(const n in t)if(n!=="default"){const r=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,r.get?r:{enumerable:!0,get:()=>t[n]})}}return e.default=t,Object.freeze(e)}const s=u(c);class b{databaseName="chatLogSel";databaseVersion=1;storeName="messages";db;constructor(){this.init()}generateId(e){return a.MD5(e).toString()}async getTranslate(e){const n=this.db.transaction(this.storeName).store;let r=this.generateId(e.text+e.toLang);try{return await n.get(r)}catch(i){console.warn(i);return}}async addTranslate(e){const n=this.db.transaction(this.storeName,"readwrite"),r=n.store,i={id:this.generateId(e.text+e.toLang),value:e.transText,toLang:e.toLang,type:"FORWARD"};try{i.value&&await r.put(i)}catch(o){console.warn("添加正向翻译失败:",o)}if(e.isInput&&e.recive_lang&&e.recive_lang!==e.toLang&&e.transText!==e.text){const o={id:this.generateId(e.transText+e.recive_lang),value:e.text,toLang:e.recive_lang,type:"REVERS"};try{o.value&&await r.put(o)}catch(d){console.warn("添加反向翻译失败:",d)}}n.done.catch(o=>{console.error("事务失败:",o)})}async init(){try{this.db=await s.openDB(this.databaseName,this.databaseVersion,{upgrade:e=>{e.createObjectStore(this.storeName,{keyPath:"id"})}})}catch(e){s.deleteDB(this.databaseName).then(async n=>{this.db=await s.openDB(this.databaseName,this.databaseVersion,{upgrade:r=>{r.createObjectStore(this.storeName,{keyPath:"id"})}})}).catch(n=>{alert("初始化数据库失败")}),console.warn("error",e)}}}exports.MyIdb=b;
//# sourceMappingURL=indexeddb.js.map
