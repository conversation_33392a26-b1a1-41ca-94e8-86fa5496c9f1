/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow: hidden;
}

/* 主容器 */
.app-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

/* 通用头部样式 */
.header {
    text-align: center;
    margin-bottom: 30px;
}

.app-icon {
    font-size: 4rem;
    margin-bottom: 15px;
    display: block;
}

.uninstall-icon {
    animation: shake 0.5s ease-in-out infinite alternate;
}

@keyframes shake {
    0% { transform: rotate(-5deg); }
    100% { transform: rotate(5deg); }
}

.app-title {
    font-size: 2.5rem;
    color: white;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-description {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 0;
}

/* 安装界面样式 */
.install-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.install-content {
    text-align: center;
}

.feature-list {
    margin: 30px 0;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.feature-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1.1rem;
    color: #333;
}

.feature-icon {
    font-size: 1.2rem;
}

.install-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 15px 40px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0 auto;
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.install-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
}

.install-button:active {
    transform: translateY(0);
}

.button-icon {
    font-size: 1.1rem;
}

/* 卸载界面样式 */
.uninstall-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 100%;
    animation: slideInUp 0.6s ease-out;
}

/* 进度容器 */
.progress-container {
    margin-top: 20px;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-text {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
}

.progress-percentage {
    font-size: 1.2rem;
    font-weight: 700;
    color: #667eea;
}

/* 进度条样式 */
.progress-bar {
    width: 100%;
    height: 12px;
    background: #f0f0f0;
    border-radius: 6px;
    overflow: hidden;
    margin-bottom: 30px;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 6px;
    transition: width 0.5s ease;
    position: relative;
    overflow: hidden;
}

.progress-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shine 2s infinite;
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 步骤指示器 */
.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e0e0e0;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all 0.3s ease;
}

.step.completed .step-circle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: scale(1.1);
}

.step-name {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.step.completed .step-name {
    color: #667eea;
    font-weight: 600;
}

/* 完成消息 */
.completion-message {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
    border-radius: 15px;
    color: white;
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.success-icon {
    font-size: 3rem;
    margin-bottom: 10px;
}

/* 错误样式 */
.error-container {
    text-align: center;
    padding: 30px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border-radius: 15px;
    color: white;
}

.error-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.error-message {
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.retry-button {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid white;
    border-radius: 25px;
    padding: 10px 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.retry-button:hover {
    background: white;
    color: #ff6b6b;
}
