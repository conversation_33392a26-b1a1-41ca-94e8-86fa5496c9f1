package backend

import "golang.org/x/sys/windows/registry"

func AddUninstallInfo(appName, installPath, uninstallCmd string) error {
	key, _, err := registry.CreateKey(registry.CURRENT_USER,
		`SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\`+appName,
		registry.SET_VALUE)
	if err != nil {
		return err
	}
	defer key.Close()

	key.SetStringValue("DisplayName", appName)
	key.SetStringValue("InstallLocation", installPath)
	key.SetStringValue("UninstallString", uninstallCmd)
	return nil
}

func RemoveUninstallInfo(appName string) error {
	return registry.DeleteKey(registry.CURRENT_USER,
		`SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\`+appName)
}
